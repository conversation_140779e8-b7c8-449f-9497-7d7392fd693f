#!/bin/bash

# AI智能记账App - iOS启动脚本
# 解决每次运行都有问题的情况

echo "🚀 启动AI智能记账App (iOS版本)"
echo "================================"

# 1. 清理缓存
echo "📦 清理缓存..."
rm -rf node_modules/.cache
rm -rf .expo

# 2. 检查iOS模拟器
echo "📱 检查iOS模拟器..."
SIMULATOR_ID=$(xcrun simctl list devices | grep "iPhone 16 Pro" | grep -v "unavailable" | head -1 | sed 's/.*(\([^)]*\)).*/\1/')

if [ -z "$SIMULATOR_ID" ]; then
    echo "❌ 未找到可用的iPhone 16 Pro模拟器"
    echo "📋 可用的模拟器："
    xcrun simctl list devices | grep "iPhone" | grep -v "unavailable"
    exit 1
fi

echo "✅ 找到模拟器: $SIMULATOR_ID"

# 3. 启动模拟器
echo "🔄 启动iOS模拟器..."
xcrun simctl boot "$SIMULATOR_ID" 2>/dev/null || echo "模拟器可能已经在运行"
open -a Simulator

# 4. 等待模拟器启动
echo "⏳ 等待模拟器启动..."
sleep 5

# 5. 启动Expo开发服务器
echo "🌐 启动Expo开发服务器..."
npx expo start --clear

echo "✅ 启动完成！"
echo "💡 在Expo开发服务器启动后，按 'i' 键在iOS模拟器中打开应用"
