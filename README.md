# AI智能记账App

一个基于React Native和Expo开发的智能记账应用，集成了AI截图识别和OCR技术，让记账变得更加简单高效。

## 🌟 主要功能

### 基础记账功能
- ✅ 手动记账（收入/支出）
- ✅ 账目分类管理
- ✅ 多账户支持
- ✅ 交易记录查看和编辑
- ✅ 基础统计图表

### AI智能功能
- 🤖 **截图自动识别记账** - 检测支付宝、微信等支付截图，自动提取交易信息
- 📷 **拍照小票识别** - OCR识别纸质小票，智能解析消费信息
- 🧠 **智能分类建议** - AI根据商家和描述自动推荐分类
- 🎯 **高准确率解析** - 结合多种AI技术提升识别准确率

### 数据分析功能（后续开发）
- 📊 消费习惯分析
- 💡 个性化理财建议
- 📈 趋势预测
- ⚠️ 异常支出检测

## 🛠️ 技术栈

### 前端
- **React Native** - 跨平台移动应用框架
- **Expo** - 开发工具链和平台
- **TypeScript** - 类型安全的JavaScript
- **Zustand** - 轻量级状态管理
- **React Navigation** - 导航库
- **React Hook Form** - 表单处理

### 后端
- **Supabase** - 后端即服务平台
- **PostgreSQL** - 关系型数据库
- **实时订阅** - 数据实时同步

### AI服务
- **OpenAI GPT-4** - 文本理解和解析
- **Google Cloud Vision** - OCR文字识别
- **自定义AI模型** - 交易信息提取

## 📁 项目结构

```
src/
├── components/          # 可复用组件
│   ├── common/         # 通用组件
│   ├── forms/          # 表单组件
│   └── charts/         # 图表组件
├── screens/            # 页面组件
│   ├── Home/           # 主页
│   ├── AddTransaction/ # 记账页面
│   ├── AICapture/      # AI拍照记账
│   ├── TransactionList/# 账目列表
│   └── Settings/       # 设置页面
├── services/           # 服务层
│   ├── supabase.ts     # 数据库服务
│   ├── aiService.ts    # AI服务
│   └── screenshotService.ts # 截图监听服务
├── stores/             # 状态管理
│   ├── appStore.ts     # 应用状态
│   ├── transactionStore.ts # 交易状态
│   └── aiStore.ts      # AI功能状态
├── navigation/         # 导航配置
├── types/              # TypeScript类型定义
└── utils/              # 工具函数
```

## 🚀 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn
- Expo CLI
- iOS模拟器或Android模拟器

### 安装依赖
```bash
npm install
```

### 配置环境变量
创建 `.env` 文件并配置以下变量：
```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_OPENAI_API_KEY=your_openai_api_key
EXPO_PUBLIC_GOOGLE_VISION_API_KEY=your_google_vision_api_key
```

### 启动开发服务器
```bash
npm start
```

### 在设备上运行
```bash
# iOS
npm run ios

# Android
npm run android

# Web
npm run web
```

## 📱 功能演示

### AI截图识别流程
1. 用户在支付宝/微信完成支付后截图
2. 应用自动检测到新截图
3. OCR识别截图中的文字信息
4. AI解析提取交易金额、商家、分类等信息
5. 弹出确认卡片，用户可一键确认或编辑
6. 自动保存到账目记录

### 手动记账流程
1. 点击"手动记账"按钮
2. 选择收入/支出类型
3. 输入金额和描述
4. 选择分类和账户
5. 保存记录

## 🔧 配置说明

### AI服务配置
在 `src/services/aiService.ts` 中配置AI服务：
- OCR服务提供商（Google Vision API）
- LLM服务提供商（OpenAI GPT-4）
- 识别置信度阈值
- 支持的图片格式

### 截图监听配置
在 `src/services/screenshotService.ts` 中配置截图监听：
- 检测间隔时间
- 自动处理开关
- 确认对话框开关
- 支持的应用列表

## 🎯 开发计划

### 第一阶段 ✅
- [x] 项目初始化和基础框架
- [x] 基础记账功能
- [x] AI服务集成
- [x] 截图识别功能

### 第二阶段 🚧
- [ ] 完善UI界面
- [ ] 添加更多页面
- [ ] 优化AI识别准确率
- [ ] 添加数据导入导出

### 第三阶段 📋
- [ ] 数据分析功能
- [ ] 个性化推荐
- [ ] 预算管理
- [ ] 多设备同步

### 第四阶段 🎨
- [ ] 主题定制
- [ ] 多语言支持
- [ ] 性能优化
- [ ] 应用商店发布

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- GitHub Issues: [项目Issues页面](https://github.com/your-username/ai-accounting-app/issues)

## 🙏 致谢

感谢以下开源项目和服务：
- React Native & Expo
- Supabase
- OpenAI
- Google Cloud Vision API
- 所有贡献者和用户

---

**注意**: 这是一个演示项目，AI功能需要配置相应的API密钥才能正常使用。请确保在生产环境中妥善保护API密钥安全。
