import { AIParseResult, OCRResult } from '../types';

// AI服务配置
const AI_CONFIG = {
  // 这里可以配置不同的AI服务提供商
  OCR_PROVIDER: 'google', // 'google' | 'azure' | 'local'
  LLM_PROVIDER: 'openai', // 'openai' | 'claude'

  // API配置 (实际使用时应该从环境变量获取)
  OPENAI_API_KEY: process.env.EXPO_PUBLIC_OPENAI_API_KEY || '',
  GOOGLE_VISION_API_KEY: process.env.EXPO_PUBLIC_GOOGLE_VISION_API_KEY || '',

  // API端点
  OPENAI_API_URL: 'https://api.openai.com/v1/chat/completions',
  GOOGLE_VISION_API_URL: 'https://vision.googleapis.com/v1/images:annotate',
};

// AI提示词模板
const AI_PROMPTS = {
  parseTransaction: `
请解析以下支付信息，提取准确的交易数据。

文本内容: {ocrText}

请严格按照JSON格式返回，不要包含任何其他文字：
{
  "amount": "金额数字(仅数字，不含符号)",
  "type": "expense或income",
  "merchant": "商家名称或收款方",
  "description": "交易描述",
  "category": "建议分类",
  "confidence": "0-1之间的置信度"
}

分类建议包括：餐饮、购物、交通、娱乐、生活缴费、医疗、教育、旅游、转账、工资、奖金、投资收益等

注意：
- 如果是转账给他人或支付，type为expense
- 如果是收到转账或收款，type为income
- 金额只返回数字，不包含货币符号
- 置信度基于文本清晰度和信息完整性
`,

  categorizeTransaction: `
基于以下信息，为交易推荐最合适的分类：

商家: {merchant}
描述: {description}
金额: {amount}

可选分类：{categories}

请返回最匹配的分类名称。
`,
};

export class AIService {
  // OCR文字识别
  static async performOCR(imageUri: string): Promise<OCRResult> {
    try {
      // 如果没有配置API密钥，使用模拟数据
      if (!AI_CONFIG.GOOGLE_VISION_API_KEY && !AI_CONFIG.OPENAI_API_KEY) {
        return await this.mockOCR(imageUri);
      }

      if (AI_CONFIG.OCR_PROVIDER === 'google') {
        return await this.googleVisionOCR(imageUri);
      } else {
        // 可以添加其他OCR提供商
        throw new Error('OCR provider not supported');
      }
    } catch (error) {
      console.error('OCR failed:', error);
      // 降级到模拟数据
      return await this.mockOCR(imageUri);
    }
  }

  // 模拟OCR（用于演示）
  private static async mockOCR(imageUri: string): Promise<OCRResult> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // 随机生成模拟的小票数据
    const mockReceipts = [
      {
        text: `麦当劳
收银员：001
2024-01-15 12:30:45
巨无霸套餐 x1    ¥35.00
薯条(大) x1      ¥12.00
可乐(中) x1      ¥8.00
小计：¥55.00
支付方式：微信支付
订单号：202401151230001`,
        confidence: 0.92,
      },
      {
        text: `星巴克咖啡
门店：北京三里屯店
2024-01-15 14:20:15
美式咖啡(大杯) x1  ¥28.00
蓝莓马芬 x1       ¥22.00
合计：¥50.00
支付方式：支付宝
会员卡：****1234`,
        confidence: 0.88,
      },
      {
        text: `滴滴出行
行程时间：2024-01-15 09:15-09:35
起点：国贸CBD
终点：望京SOHO
里程：12.5公里
车型：快车
费用：¥25.80
支付方式：微信支付`,
        confidence: 0.85,
      },
      {
        text: `盒马鲜生
购物清单
2024-01-15 18:45:20
有机鸡蛋 x1      ¥15.90
新鲜牛奶 x2      ¥26.80
苹果 1.2kg       ¥18.60
青菜 0.8kg       ¥8.40
合计：¥69.70
支付方式：花呗`,
        confidence: 0.9,
      },
    ];

    const randomReceipt =
      mockReceipts[Math.floor(Math.random() * mockReceipts.length)];

    return {
      text: randomReceipt.text,
      confidence: randomReceipt.confidence,
    };
  }

  // Google Vision OCR
  private static async googleVisionOCR(imageUri: string): Promise<OCRResult> {
    // 将图片转换为base64
    const base64Image = await this.imageToBase64(imageUri);

    const requestBody = {
      requests: [
        {
          image: {
            content: base64Image,
          },
          features: [
            {
              type: 'TEXT_DETECTION',
              maxResults: 1,
            },
          ],
        },
      ],
    };

    const response = await fetch(
      `${AI_CONFIG.GOOGLE_VISION_API_URL}?key=${AI_CONFIG.GOOGLE_VISION_API_KEY}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      throw new Error(`Google Vision API error: ${response.status}`);
    }

    const result = await response.json();
    const textAnnotations = result.responses[0]?.textAnnotations;

    if (!textAnnotations || textAnnotations.length === 0) {
      return {
        text: '',
        confidence: 0,
      };
    }

    return {
      text: textAnnotations[0].description,
      confidence: textAnnotations[0].confidence || 0.8,
      bounding_boxes: textAnnotations.slice(1).map((annotation: any) => ({
        text: annotation.description,
        x: annotation.boundingPoly.vertices[0].x,
        y: annotation.boundingPoly.vertices[0].y,
        width:
          annotation.boundingPoly.vertices[2].x -
          annotation.boundingPoly.vertices[0].x,
        height:
          annotation.boundingPoly.vertices[2].y -
          annotation.boundingPoly.vertices[0].y,
      })),
    };
  }

  // AI解析交易信息
  static async parseTransactionFromText(
    ocrText: string,
    userCategories: string[] = []
  ): Promise<AIParseResult> {
    try {
      // 如果没有配置API密钥，使用模拟解析
      if (!AI_CONFIG.OPENAI_API_KEY) {
        return await this.mockParse(ocrText);
      }

      if (AI_CONFIG.LLM_PROVIDER === 'openai') {
        return await this.openAIParse(ocrText, userCategories);
      } else {
        throw new Error('LLM provider not supported');
      }
    } catch (error) {
      console.error('AI parsing failed:', error);
      // 降级到模拟解析
      return await this.mockParse(ocrText);
    }
  }

  // 模拟AI解析（用于演示）
  private static async mockParse(ocrText: string): Promise<AIParseResult> {
    // 模拟AI处理延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 简单的规则解析
    const text = ocrText.toLowerCase();

    // 提取金额
    const amountMatch = text.match(/[¥￥]?(\d+\.?\d*)/);
    const amount = amountMatch ? parseFloat(amountMatch[1]) : 0;

    // 判断类型和分类
    let type: 'income' | 'expense' = 'expense';
    let category = '其他';
    let merchant = '';
    let description = '';

    if (text.includes('麦当劳') || text.includes('肯德基')) {
      category = '餐饮';
      merchant = text.includes('麦当劳') ? '麦当劳' : '肯德基';
      description = '快餐消费';
    } else if (text.includes('星巴克') || text.includes('咖啡')) {
      category = '餐饮';
      merchant = '星巴克';
      description = '咖啡消费';
    } else if (
      text.includes('滴滴') ||
      text.includes('出行') ||
      text.includes('打车')
    ) {
      category = '交通';
      merchant = '滴滴出行';
      description = '打车费用';
    } else if (
      text.includes('超市') ||
      text.includes('盒马') ||
      text.includes('购物')
    ) {
      category = '购物';
      merchant = text.includes('盒马') ? '盒马鲜生' : '超市';
      description = '日用品购买';
    } else if (
      text.includes('工资') ||
      text.includes('薪水') ||
      text.includes('收入')
    ) {
      type = 'income';
      category = '工资';
      description = '工资收入';
    }

    return {
      amount,
      type,
      merchant,
      description,
      category,
      confidence: 0.85,
      raw_text: ocrText,
    };
  }

  // OpenAI解析
  private static async openAIParse(
    ocrText: string,
    userCategories: string[]
  ): Promise<AIParseResult> {
    const prompt = AI_PROMPTS.parseTransaction.replace('{ocrText}', ocrText);

    const requestBody = {
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content:
            '你是一个专业的财务助手，擅长从支付截图和小票中提取交易信息。',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      temperature: 0.1,
      max_tokens: 500,
    };

    const response = await fetch(AI_CONFIG.OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${AI_CONFIG.OPENAI_API_KEY}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const result = await response.json();
    const content = result.choices[0]?.message?.content;

    if (!content) {
      throw new Error('No response from AI');
    }

    try {
      // 尝试解析JSON响应
      const parsed = JSON.parse(content);

      return {
        amount: parseFloat(parsed.amount) || 0,
        type: parsed.type === 'income' ? 'income' : 'expense',
        merchant: parsed.merchant || '',
        description: parsed.description || '',
        category: parsed.category || '',
        confidence: parseFloat(parsed.confidence) || 0.5,
        raw_text: ocrText,
      };
    } catch (parseError) {
      console.error('Failed to parse AI response:', parseError);
      throw new Error('AI响应格式错误');
    }
  }

  // 智能分类建议
  static async suggestCategory(
    merchant: string,
    description: string,
    amount: number,
    availableCategories: string[]
  ): Promise<string> {
    try {
      const prompt = AI_PROMPTS.categorizeTransaction
        .replace('{merchant}', merchant)
        .replace('{description}', description)
        .replace('{amount}', amount.toString())
        .replace('{categories}', availableCategories.join(', '));

      const requestBody = {
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.1,
        max_tokens: 50,
      };

      const response = await fetch(AI_CONFIG.OPENAI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${AI_CONFIG.OPENAI_API_KEY}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        return availableCategories[0] || '其他';
      }

      const result = await response.json();
      const suggestedCategory = result.choices[0]?.message?.content?.trim();

      // 验证建议的分类是否在可用列表中
      if (availableCategories.includes(suggestedCategory)) {
        return suggestedCategory;
      }

      return availableCategories[0] || '其他';
    } catch (error) {
      console.error('Category suggestion failed:', error);
      return availableCategories[0] || '其他';
    }
  }

  // 图片转base64
  private static async imageToBase64(imageUri: string): Promise<string> {
    try {
      const response = await fetch(imageUri);
      const blob = await response.blob();

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const base64 = (reader.result as string).split(',')[1];
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      throw new Error('图片转换失败');
    }
  }

  // 预处理图片（提高OCR准确率）
  static async preprocessImage(imageUri: string): Promise<string> {
    // 这里可以添加图片预处理逻辑
    // 比如调整对比度、去噪、旋转等
    // 目前直接返回原图
    return imageUri;
  }

  // 验证AI解析结果
  static validateParseResult(result: AIParseResult): boolean {
    return (
      result.amount > 0 &&
      ['income', 'expense'].includes(result.type) &&
      result.confidence > 0.3
    );
  }
}
