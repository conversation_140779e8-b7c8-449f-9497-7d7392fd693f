import { Alert } from 'react-native';
// import * as MediaLibrary from 'expo-media-library';
import { AIService } from './aiService';
import { AIParseResult } from '../types';

export interface ScreenshotDetectionOptions {
  enabled: boolean;
  autoProcess: boolean;
  showConfirmDialog: boolean;
  supportedApps: string[];
}

export class ScreenshotService {
  private static isListening = false;
  private static lastScreenshotTime = 0;
  private static options: ScreenshotDetectionOptions = {
    enabled: true,
    autoProcess: true,
    showConfirmDialog: true,
    supportedApps: ['支付宝', '微信', '银行', '淘宝', '京东'],
  };

  private static callbacks: Array<(result: AIParseResult) => void> = [];

  // 开始监听截图
  static async startListening(options?: Partial<ScreenshotDetectionOptions>) {
    if (this.isListening) {
      console.log('Screenshot service is already listening');
      return;
    }

    // 更新配置
    if (options) {
      this.options = { ...this.options, ...options };
    }

    // TODO: 在移动端实现时需要请求媒体库权限
    // const { status } = await MediaLibrary.requestPermissionsAsync();
    // if (status !== 'granted') {
    //   throw new Error('需要相册权限来检测截图');
    // }

    this.isListening = true;
    // this.startPolling(); // 暂时禁用轮询
    console.log('Screenshot detection started (web mode)');
  }

  // 停止监听
  static stopListening() {
    this.isListening = false;
    console.log('Screenshot detection stopped');
  }

  // 添加回调函数
  static addCallback(callback: (result: AIParseResult) => void) {
    this.callbacks.push(callback);
  }

  // 移除回调函数
  static removeCallback(callback: (result: AIParseResult) => void) {
    const index = this.callbacks.indexOf(callback);
    if (index > -1) {
      this.callbacks.splice(index, 1);
    }
  }

  // 轮询检测新截图
  private static async startPolling() {
    const checkInterval = 2000; // 每2秒检查一次

    const poll = async () => {
      if (!this.isListening) return;

      try {
        await this.checkForNewScreenshots();
      } catch (error) {
        console.error('Screenshot detection error:', error);
      }

      // 继续轮询
      setTimeout(poll, checkInterval);
    };

    poll();
  }

  // 检查新截图
  private static async checkForNewScreenshots() {
    try {
      // TODO: 在移动端实现时获取最近的截图
      // const recentMedia = await MediaLibrary.getAssetsAsync({
      //   mediaType: 'photo',
      //   sortBy: 'creationTime',
      //   first: 5, // 检查最近5张图片
      // });

      // for (const asset of recentMedia.assets) {
      //   // 检查是否是新截图
      //   if (this.isRecentScreenshot(asset)) {
      //     await this.handleNewScreenshot(asset);
      //     break; // 只处理最新的一张
      //   }
      // }
      console.log('Screenshot check (web mode - not implemented)');
    } catch (error) {
      console.error('Failed to check screenshots:', error);
    }
  }

  // 判断是否是最近的截图
  private static isRecentScreenshot(asset: any): boolean {
    const now = Date.now();
    const assetTime = asset.creationTime;
    const timeDiff = now - assetTime;

    // 检查是否是最近30秒内的截图
    const isRecent = timeDiff < 30000;

    // 检查是否已经处理过
    const isNew = assetTime > this.lastScreenshotTime;

    // 检查文件名是否包含截图关键词
    const isScreenshot =
      asset.filename.toLowerCase().includes('screenshot') ||
      asset.filename.toLowerCase().includes('屏幕截图') ||
      asset.filename.toLowerCase().includes('截屏');

    return isRecent && isNew && isScreenshot;
  }

  // 处理新截图
  private static async handleNewScreenshot(asset: any) {
    try {
      this.lastScreenshotTime = asset.creationTime;

      if (!this.options.enabled) {
        return;
      }

      // 获取图片URI
      // const assetInfo = await MediaLibrary.getAssetInfoAsync(asset);
      const imageUri = asset.localUri || asset.uri;

      if (this.options.showConfirmDialog) {
        // 显示确认对话框
        this.showProcessConfirmDialog(imageUri);
      } else if (this.options.autoProcess) {
        // 自动处理
        await this.processScreenshot(imageUri);
      }
    } catch (error) {
      console.error('Failed to handle screenshot:', error);
    }
  }

  // 显示处理确认对话框
  private static showProcessConfirmDialog(imageUri: string) {
    Alert.alert(
      '检测到新截图',
      '是否要分析这张截图并自动记账？',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '分析',
          onPress: () => this.processScreenshot(imageUri),
        },
      ],
      { cancelable: true }
    );
  }

  // 处理截图
  private static async processScreenshot(imageUri: string) {
    try {
      console.log('Processing screenshot:', imageUri);

      // 预处理图片
      const processedImageUri = await AIService.preprocessImage(imageUri);

      // OCR识别
      const ocrResult = await AIService.performOCR(processedImageUri);

      if (!ocrResult.text || ocrResult.text.trim().length === 0) {
        this.showError('未能识别到文字内容');
        return;
      }

      // AI解析交易信息
      const parseResult = await AIService.parseTransactionFromText(
        ocrResult.text
      );

      // 验证解析结果
      if (!AIService.validateParseResult(parseResult)) {
        this.showError('未能识别到有效的交易信息');
        return;
      }

      // 通知回调函数
      this.notifyCallbacks(parseResult);
    } catch (error) {
      console.error('Screenshot processing failed:', error);
      this.showError('截图分析失败，请重试');
    }
  }

  // 通知所有回调函数
  private static notifyCallbacks(result: AIParseResult) {
    this.callbacks.forEach((callback) => {
      try {
        callback(result);
      } catch (error) {
        console.error('Callback error:', error);
      }
    });
  }

  // 显示错误信息
  private static showError(message: string) {
    Alert.alert('处理失败', message, [{ text: '确定' }]);
  }

  // 手动处理图片
  static async processImage(imageUri: string): Promise<AIParseResult> {
    try {
      // 预处理图片
      const processedImageUri = await AIService.preprocessImage(imageUri);

      // OCR识别
      const ocrResult = await AIService.performOCR(processedImageUri);

      if (!ocrResult.text || ocrResult.text.trim().length === 0) {
        throw new Error('未能识别到文字内容');
      }

      // AI解析交易信息
      const parseResult = await AIService.parseTransactionFromText(
        ocrResult.text
      );

      // 验证解析结果
      if (!AIService.validateParseResult(parseResult)) {
        throw new Error('未能识别到有效的交易信息');
      }

      return parseResult;
    } catch (error) {
      console.error('Image processing failed:', error);
      throw error;
    }
  }

  // 更新配置
  static updateOptions(newOptions: Partial<ScreenshotDetectionOptions>) {
    this.options = { ...this.options, ...newOptions };
  }

  // 获取当前配置
  static getOptions(): ScreenshotDetectionOptions {
    return { ...this.options };
  }

  // 检查是否正在监听
  static isListeningActive(): boolean {
    return this.isListening;
  }
}
