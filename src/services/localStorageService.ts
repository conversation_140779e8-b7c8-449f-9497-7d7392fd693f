import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Transaction, Account, Category, User } from '../types';
import { DEFAULT_CATEGORIES, DEFAULT_ACCOUNT_TYPES } from '../utils/constants';

// 本地存储键名
const STORAGE_KEYS = {
  USER: 'user',
  ACCOUNTS: 'accounts',
  CATEGORIES: 'categories',
  TRANSACTIONS: 'transactions',
  SETTINGS: 'settings',
} as const;

// 本地存储适配器
class StorageAdapter {
  static async getItem(key: string): Promise<string | null> {
    if (Platform.OS === 'web') {
      return localStorage.getItem(key);
    } else {
      return await AsyncStorage.getItem(key);
    }
  }

  static async setItem(key: string, value: string): Promise<void> {
    if (Platform.OS === 'web') {
      localStorage.setItem(key, value);
    } else {
      await AsyncStorage.setItem(key, value);
    }
  }

  static async removeItem(key: string): Promise<void> {
    if (Platform.OS === 'web') {
      localStorage.removeItem(key);
    } else {
      await AsyncStorage.removeItem(key);
    }
  }
}

// 生成唯一ID
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// 本地数据库服务
export class LocalStorageService {
  // 初始化默认数据
  static async initializeDefaultData(userId: string): Promise<void> {
    // 创建默认账户
    const defaultAccounts: Account[] = [
      {
        id: generateId(),
        user_id: userId,
        name: '现金',
        type: 'cash',
        balance: 1000,
        currency: 'CNY',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: generateId(),
        user_id: userId,
        name: '银行卡',
        type: 'bank',
        balance: 5000,
        currency: 'CNY',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    // 创建默认分类
    const defaultCategoriesData: Category[] = [
      ...DEFAULT_CATEGORIES.expense.map((cat) => ({
        id: generateId(),
        user_id: userId,
        name: cat.name,
        type: 'expense' as const,
        icon: cat.icon,
        color: cat.color,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })),
      ...DEFAULT_CATEGORIES.income.map((cat) => ({
        id: generateId(),
        user_id: userId,
        name: cat.name,
        type: 'income' as const,
        icon: cat.icon,
        color: cat.color,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })),
    ];

    // 创建示例交易记录
    const sampleTransactions: Transaction[] = [
      {
        id: generateId(),
        user_id: userId,
        account_id: defaultAccounts[0].id,
        category_id:
          defaultCategoriesData.find((c) => c.name === '餐饮')?.id || '',
        amount: 25.5,
        type: 'expense',
        description: '午餐',
        transaction_date: new Date().toISOString(),
        is_ai_generated: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: generateId(),
        user_id: userId,
        account_id: defaultAccounts[1].id,
        category_id:
          defaultCategoriesData.find((c) => c.name === '工资')?.id || '',
        amount: 8000,
        type: 'income',
        description: '月薪',
        transaction_date: new Date(
          Date.now() - 24 * 60 * 60 * 1000
        ).toISOString(),
        is_ai_generated: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    // 保存到本地存储
    await this.saveAccounts(defaultAccounts);
    await this.saveCategories(defaultCategoriesData);
    await this.saveTransactions(sampleTransactions);
  }

  // 用户相关操作
  static async getCurrentUser(): Promise<User | null> {
    const userData = await StorageAdapter.getItem(STORAGE_KEYS.USER);
    return userData ? JSON.parse(userData) : null;
  }

  static async saveUser(user: User): Promise<void> {
    await StorageAdapter.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
  }

  static async signOut(): Promise<void> {
    await StorageAdapter.removeItem(STORAGE_KEYS.USER);
  }

  // 账户相关操作
  static async getAccounts(userId: string): Promise<Account[]> {
    const accountsData = await StorageAdapter.getItem(STORAGE_KEYS.ACCOUNTS);
    const accounts: Account[] = accountsData ? JSON.parse(accountsData) : [];
    return accounts.filter(
      (account) => account.user_id === userId && account.is_active
    );
  }

  static async saveAccounts(accounts: Account[]): Promise<void> {
    await StorageAdapter.setItem(
      STORAGE_KEYS.ACCOUNTS,
      JSON.stringify(accounts)
    );
  }

  static async createAccount(
    accountData: Omit<Account, 'id' | 'created_at' | 'updated_at'>
  ): Promise<Account> {
    const accountsData = await StorageAdapter.getItem(STORAGE_KEYS.ACCOUNTS);
    const allAccounts: Account[] = accountsData ? JSON.parse(accountsData) : [];

    const newAccount: Account = {
      ...accountData,
      id: generateId(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    allAccounts.push(newAccount);
    await this.saveAccounts(allAccounts);
    return newAccount;
  }

  // 分类相关操作
  static async getCategories(
    userId: string,
    type?: 'income' | 'expense'
  ): Promise<Category[]> {
    const categoriesData = await StorageAdapter.getItem(
      STORAGE_KEYS.CATEGORIES
    );
    const categories: Category[] = categoriesData
      ? JSON.parse(categoriesData)
      : [];

    let filteredCategories = categories.filter(
      (category) => category.user_id === userId && category.is_active
    );

    if (type) {
      filteredCategories = filteredCategories.filter(
        (category) => category.type === type
      );
    }

    return filteredCategories;
  }

  static async saveCategories(categories: Category[]): Promise<void> {
    await StorageAdapter.setItem(
      STORAGE_KEYS.CATEGORIES,
      JSON.stringify(categories)
    );
  }

  static async createCategory(
    categoryData: Omit<Category, 'id' | 'created_at' | 'updated_at'>
  ): Promise<Category> {
    const categoriesData = await StorageAdapter.getItem(
      STORAGE_KEYS.CATEGORIES
    );
    const allCategories: Category[] = categoriesData
      ? JSON.parse(categoriesData)
      : [];

    const newCategory: Category = {
      ...categoryData,
      id: generateId(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    allCategories.push(newCategory);
    await this.saveCategories(allCategories);
    return newCategory;
  }

  // 交易记录相关操作
  static async getTransactions(
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      type?: 'income' | 'expense';
      category_id?: string;
      account_id?: string;
      date_range?: { start: string; end: string };
    } = {}
  ): Promise<Transaction[]> {
    const transactionsData = await StorageAdapter.getItem(
      STORAGE_KEYS.TRANSACTIONS
    );
    const accounts = await this.getAccounts(userId);
    const categories = await this.getCategories(userId);

    let transactions: Transaction[] = transactionsData
      ? JSON.parse(transactionsData)
      : [];

    // 过滤用户的交易记录
    transactions = transactions.filter(
      (transaction) => transaction.user_id === userId
    );

    // 应用筛选条件
    if (options.type) {
      transactions = transactions.filter((t) => t.type === options.type);
    }
    if (options.category_id) {
      transactions = transactions.filter(
        (t) => t.category_id === options.category_id
      );
    }
    if (options.account_id) {
      transactions = transactions.filter(
        (t) => t.account_id === options.account_id
      );
    }
    if (options.date_range) {
      transactions = transactions.filter(
        (t) =>
          t.transaction_date >= options.date_range!.start &&
          t.transaction_date <= options.date_range!.end
      );
    }

    // 排序
    transactions.sort((a, b) => {
      const dateCompare =
        new Date(b.transaction_date).getTime() -
        new Date(a.transaction_date).getTime();
      if (dateCompare !== 0) return dateCompare;
      return (
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    });

    // 添加关联数据
    transactions = transactions.map((transaction) => ({
      ...transaction,
      account: accounts.find((a) => a.id === transaction.account_id),
      category: categories.find((c) => c.id === transaction.category_id),
    }));

    // 分页
    if (options.offset || options.limit) {
      const start = options.offset || 0;
      const end = start + (options.limit || 10);
      transactions = transactions.slice(start, end);
    }

    return transactions;
  }

  static async saveTransactions(transactions: Transaction[]): Promise<void> {
    await StorageAdapter.setItem(
      STORAGE_KEYS.TRANSACTIONS,
      JSON.stringify(transactions)
    );
  }

  static async createTransaction(
    transactionData: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>
  ): Promise<Transaction> {
    const transactionsData = await StorageAdapter.getItem(
      STORAGE_KEYS.TRANSACTIONS
    );
    const transactions: Transaction[] = transactionsData
      ? JSON.parse(transactionsData)
      : [];

    const newTransaction: Transaction = {
      ...transactionData,
      id: generateId(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    transactions.push(newTransaction);
    await this.saveTransactions(transactions);

    // 返回包含关联数据的交易记录
    const accounts = await this.getAccounts(transactionData.user_id);
    const categories = await this.getCategories(transactionData.user_id);

    return {
      ...newTransaction,
      account: accounts.find((a) => a.id === newTransaction.account_id),
      category: categories.find((c) => c.id === newTransaction.category_id),
    };
  }

  static async updateTransaction(
    id: string,
    updates: Partial<Transaction>
  ): Promise<Transaction> {
    const transactionsData = await StorageAdapter.getItem(
      STORAGE_KEYS.TRANSACTIONS
    );
    const transactions: Transaction[] = transactionsData
      ? JSON.parse(transactionsData)
      : [];

    const index = transactions.findIndex((t) => t.id === id);
    if (index === -1) {
      throw new Error('Transaction not found');
    }

    transactions[index] = {
      ...transactions[index],
      ...updates,
      updated_at: new Date().toISOString(),
    };

    await this.saveTransactions(transactions);
    return transactions[index];
  }

  static async deleteTransaction(id: string): Promise<void> {
    const transactionsData = await StorageAdapter.getItem(
      STORAGE_KEYS.TRANSACTIONS
    );
    const transactions: Transaction[] = transactionsData
      ? JSON.parse(transactionsData)
      : [];

    const filteredTransactions = transactions.filter((t) => t.id !== id);
    await this.saveTransactions(filteredTransactions);
  }
}
