import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { RootStackParamList, TabParamList } from '../types';

// 导入屏幕组件
import HomeScreen from '../screens/Home/HomeScreen';
import AddTransactionScreen from '../screens/AddTransaction/AddTransactionScreen';
import AICaptureScreen from '../screens/AICapture/AICaptureScreen';
import TransactionListScreen from '../screens/TransactionList/TransactionListScreen';
import TransactionDetailScreen from '../screens/TransactionDetail/TransactionDetailScreen';
import SettingsScreen from '../screens/Settings/SettingsScreen';
import AccountManagementScreen from '../screens/Settings/AccountManagementScreen';
import CategoryManagementScreen from '../screens/Settings/CategoryManagementScreen';
import StatsScreen from '../screens/Stats/StatsScreen';

import { View, Text, StyleSheet } from 'react-native';

// 临时占位组件
const PlaceholderScreen = ({ title }: { title: string }) => (
  <View style={styles.placeholder}>
    <Text style={styles.placeholderTitle}>{title}</Text>
    <Text style={styles.placeholderText}>功能开发中...</Text>
  </View>
);

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();

// 底部标签导航
const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'HomeTab':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'TransactionsTab':
              iconName = focused ? 'list' : 'list-outline';
              break;
            case 'AITab':
              iconName = focused ? 'camera' : 'camera-outline';
              break;
            case 'StatsTab':
              iconName = focused ? 'bar-chart' : 'bar-chart-outline';
              break;
            case 'SettingsTab':
              iconName = focused ? 'settings' : 'settings-outline';
              break;
            default:
              iconName = 'circle';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: '#8E8E93',
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopWidth: 0,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          height: 88,
          paddingBottom: 34,
          paddingTop: 8,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{
          tabBarLabel: '首页',
        }}
      />
      <Tab.Screen
        name="TransactionsTab"
        options={{
          tabBarLabel: '账目',
        }}
      >
        {() => <PlaceholderScreen title="账目列表" />}
      </Tab.Screen>
      <Tab.Screen
        name="AITab"
        options={{
          tabBarLabel: 'AI记账',
        }}
      >
        {() => <PlaceholderScreen title="AI拍照记账" />}
      </Tab.Screen>
      <Tab.Screen
        name="StatsTab"
        component={StatsScreen}
        options={{
          tabBarLabel: '统计',
        }}
      />
      <Tab.Screen
        name="SettingsTab"
        component={SettingsScreen}
        options={{
          tabBarLabel: '设置',
        }}
      />
    </Tab.Navigator>
  );
};

// 主导航器
const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: true,
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen name="Home" component={TabNavigator} />
        <Stack.Screen
          name="AddTransaction"
          component={AddTransactionScreen}
          options={{
            headerShown: true,
            title: '记账',
            headerStyle: {
              backgroundColor: '#FFFFFF',
            },
            headerTintColor: '#007AFF',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
            },
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name="AICapture"
          component={AICaptureScreen}
          options={{
            headerShown: true,
            title: 'AI拍照记账',
            headerStyle: {
              backgroundColor: '#FFFFFF',
            },
            headerTintColor: '#007AFF',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
            },
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name="TransactionList"
          component={TransactionListScreen}
          options={{
            headerShown: true,
            title: '交易记录',
            headerStyle: {
              backgroundColor: '#FFFFFF',
            },
            headerTintColor: '#007AFF',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
            },
          }}
        />
        <Stack.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            headerShown: true,
            title: '设置',
            headerStyle: {
              backgroundColor: '#FFFFFF',
            },
            headerTintColor: '#007AFF',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
            },
          }}
        />
        <Stack.Screen
          name="TransactionDetail"
          component={TransactionDetailScreen}
          options={{
            headerShown: true,
            title: '交易详情',
            headerStyle: {
              backgroundColor: '#FFFFFF',
            },
            headerTintColor: '#007AFF',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
            },
          }}
        />
        <Stack.Screen
          name="AccountManagement"
          component={AccountManagementScreen}
          options={{
            headerShown: true,
            title: '账户管理',
            headerStyle: {
              backgroundColor: '#FFFFFF',
            },
            headerTintColor: '#007AFF',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
            },
          }}
        />
        <Stack.Screen
          name="CategoryManagement"
          component={CategoryManagementScreen}
          options={{
            headerShown: true,
            title: '分类管理',
            headerStyle: {
              backgroundColor: '#FFFFFF',
            },
            headerTintColor: '#007AFF',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
            },
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  placeholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
    padding: 20,
  },
  placeholderTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  placeholderText: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
  },
});

export default AppNavigator;
