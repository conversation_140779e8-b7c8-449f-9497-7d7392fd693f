import { create } from 'zustand';
import { User, AppError } from '../types';
import { LocalStorageService } from '../services/localStorageService';

interface AppState {
  // 用户状态
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: AppError | null;

  // 应用设置
  theme: 'light' | 'dark' | 'system';
  currency: string;
  language: string;

  // 操作方法
  initializeApp: () => Promise<void>;
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: AppError | null) => void;
  clearError: () => void;
  signOut: () => Promise<void>;
  updateSettings: (
    settings: Partial<{
      theme: 'light' | 'dark' | 'system';
      currency: string;
      language: string;
    }>
  ) => void;
}

export const useAppStore = create<AppState>((set, get) => ({
  // 初始状态
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  theme: 'system',
  currency: 'CNY',
  language: 'zh-CN',

  // 初始化应用
  initializeApp: async () => {
    try {
      set({ isLoading: true });

      // 尝试获取现有用户
      let user = await LocalStorageService.getCurrentUser();

      // 如果没有用户，创建默认用户
      if (!user) {
        user = {
          id: Date.now().toString(),
          email: '<EMAIL>',
          name: '演示用户',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        await LocalStorageService.saveUser(user);
        await LocalStorageService.initializeDefaultData(user.id);
      }

      set({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: {
          code: 'INIT_ERROR',
          message: '应用初始化失败',
          details: error,
        },
      });
    }
  },

  // 设置用户
  setUser: (user) => {
    set({
      user,
      isAuthenticated: !!user,
      error: null,
    });
  },

  // 设置加载状态
  setLoading: (isLoading) => {
    set({ isLoading });
  },

  // 设置错误
  setError: (error) => {
    set({ error, isLoading: false });
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 登出
  signOut: async () => {
    try {
      set({ isLoading: true });
      await LocalStorageService.signOut();
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: {
          code: 'SIGN_OUT_ERROR',
          message: '登出失败',
          details: error,
        },
      });
    }
  },

  // 更新设置
  updateSettings: (settings) => {
    set((state) => ({
      ...state,
      ...settings,
    }));
  },
}));
