import { create } from 'zustand';
import { Transaction, Account, Category, AppError } from '../types';
import { LocalStorageService } from '../services/localStorageService';

interface TransactionState {
  // 数据状态
  transactions: Transaction[];
  accounts: Account[];
  categories: Category[];

  // 加载状态
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  error: AppError | null;

  // 筛选状态
  filter: {
    type?: 'income' | 'expense';
    category_id?: string;
    account_id?: string;
    date_range?: {
      start: string;
      end: string;
    };
  };

  // 分页状态
  pagination: {
    page: number;
    limit: number;
    hasMore: boolean;
  };

  // 操作方法
  loadTransactions: (userId: string, refresh?: boolean) => Promise<void>;
  loadAccounts: (userId: string) => Promise<void>;
  loadCategories: (
    userId: string,
    type?: 'income' | 'expense'
  ) => Promise<void>;
  createTransaction: (transactionData: any) => Promise<Transaction>;
  updateTransaction: (id: string, updates: any) => Promise<Transaction>;
  deleteTransaction: (id: string) => Promise<void>;
  setFilter: (filter: Partial<TransactionState['filter']>) => void;
  clearFilter: () => void;
  setError: (error: AppError | null) => void;
  clearError: () => void;
}

export const useTransactionStore = create<TransactionState>((set, get) => ({
  // 初始状态
  transactions: [],
  accounts: [],
  categories: [],
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  error: null,
  filter: {},
  pagination: {
    page: 1,
    limit: 20,
    hasMore: true,
  },

  // 加载交易记录
  loadTransactions: async (userId: string, refresh = false) => {
    try {
      const state = get();

      if (refresh) {
        set({
          isLoading: true,
          error: null,
          pagination: { ...state.pagination, page: 1, hasMore: true },
        });
      } else if (state.isLoading || !state.pagination.hasMore) {
        return;
      } else {
        set({ isLoading: true, error: null });
      }

      const offset = refresh
        ? 0
        : (state.pagination.page - 1) * state.pagination.limit;

      const transactions = await LocalStorageService.getTransactions(userId, {
        ...state.filter,
        limit: state.pagination.limit,
        offset,
      });

      set((prevState) => ({
        transactions: refresh
          ? transactions
          : [...prevState.transactions, ...transactions],
        isLoading: false,
        pagination: {
          ...prevState.pagination,
          page: refresh ? 2 : prevState.pagination.page + 1,
          hasMore: transactions.length === prevState.pagination.limit,
        },
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: {
          code: 'LOAD_TRANSACTIONS_ERROR',
          message: '加载交易记录失败',
          details: error,
        },
      });
    }
  },

  // 加载账户
  loadAccounts: async (userId: string) => {
    try {
      const accounts = await LocalStorageService.getAccounts(userId);
      set({ accounts, error: null });
    } catch (error) {
      set({
        error: {
          code: 'LOAD_ACCOUNTS_ERROR',
          message: '加载账户失败',
          details: error,
        },
      });
    }
  },

  // 加载分类
  loadCategories: async (userId: string, type?: 'income' | 'expense') => {
    try {
      const categories = await LocalStorageService.getCategories(userId, type);
      set({ categories, error: null });
    } catch (error) {
      set({
        error: {
          code: 'LOAD_CATEGORIES_ERROR',
          message: '加载分类失败',
          details: error,
        },
      });
    }
  },

  // 创建交易记录
  createTransaction: async (transactionData: any) => {
    try {
      set({ isCreating: true, error: null });

      const transaction = await LocalStorageService.createTransaction(
        transactionData
      );

      set((state) => ({
        transactions: [transaction, ...state.transactions],
        isCreating: false,
      }));

      return transaction;
    } catch (error) {
      set({
        isCreating: false,
        error: {
          code: 'CREATE_TRANSACTION_ERROR',
          message: '创建交易记录失败',
          details: error,
        },
      });
      throw error;
    }
  },

  // 更新交易记录
  updateTransaction: async (id: string, updates: any) => {
    try {
      set({ isUpdating: true, error: null });

      const updatedTransaction = await LocalStorageService.updateTransaction(
        id,
        updates
      );

      set((state) => ({
        transactions: state.transactions.map((t) =>
          t.id === id ? updatedTransaction : t
        ),
        isUpdating: false,
      }));

      return updatedTransaction;
    } catch (error) {
      set({
        isUpdating: false,
        error: {
          code: 'UPDATE_TRANSACTION_ERROR',
          message: '更新交易记录失败',
          details: error,
        },
      });
      throw error;
    }
  },

  // 删除交易记录
  deleteTransaction: async (id: string) => {
    try {
      set({ isUpdating: true, error: null });

      await LocalStorageService.deleteTransaction(id);

      set((state) => ({
        transactions: state.transactions.filter((t) => t.id !== id),
        isUpdating: false,
      }));
    } catch (error) {
      set({
        isUpdating: false,
        error: {
          code: 'DELETE_TRANSACTION_ERROR',
          message: '删除交易记录失败',
          details: error,
        },
      });
      throw error;
    }
  },

  // 设置筛选条件
  setFilter: (newFilter) => {
    set((state) => ({
      filter: { ...state.filter, ...newFilter },
      pagination: { ...state.pagination, page: 1, hasMore: true },
    }));
  },

  // 清除筛选条件
  clearFilter: () => {
    set({
      filter: {},
      pagination: { page: 1, limit: 20, hasMore: true },
    });
  },

  // 设置错误
  setError: (error) => {
    set({ error });
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },
}));
