import { create } from 'zustand';
import { AIParseResult, AppError } from '../types';
import { ScreenshotService } from '../services/screenshotService';

interface AIState {
  // AI处理状态
  isProcessing: boolean;
  isOCRProcessing: boolean;
  isParsingProcessing: boolean;
  
  // 结果状态
  lastParseResult: AIParseResult | null;
  parseHistory: AIParseResult[];
  
  // 错误状态
  error: AppError | null;
  
  // 截图监听状态
  isScreenshotListening: boolean;
  screenshotOptions: {
    enabled: boolean;
    autoProcess: boolean;
    showConfirmDialog: boolean;
  };

  // 统计信息
  stats: {
    totalProcessed: number;
    successfulParses: number;
    averageConfidence: number;
  };

  // 操作方法
  processImage: (imageUri: string) => Promise<AIParseResult>;
  clearLastResult: () => void;
  clearHistory: () => void;
  setError: (error: AppError | null) => void;
  clearError: () => void;
  
  // 截图监听相关
  startScreenshotListening: () => Promise<void>;
  stopScreenshotListening: () => void;
  updateScreenshotOptions: (options: Partial<AIState['screenshotOptions']>) => void;
  
  // 统计相关
  updateStats: (result: AIParseResult) => void;
  resetStats: () => void;
}

export const useAIStore = create<AIState>((set, get) => ({
  // 初始状态
  isProcessing: false,
  isOCRProcessing: false,
  isParsingProcessing: false,
  lastParseResult: null,
  parseHistory: [],
  error: null,
  isScreenshotListening: false,
  screenshotOptions: {
    enabled: true,
    autoProcess: false,
    showConfirmDialog: true,
  },
  stats: {
    totalProcessed: 0,
    successfulParses: 0,
    averageConfidence: 0,
  },

  // 处理图片
  processImage: async (imageUri: string) => {
    try {
      set({ 
        isProcessing: true, 
        isOCRProcessing: true,
        error: null 
      });

      // 使用截图服务处理图片
      const result = await ScreenshotService.processImage(imageUri);

      set({ 
        isOCRProcessing: false,
        isParsingProcessing: false,
        isProcessing: false,
        lastParseResult: result,
      });

      // 添加到历史记录
      set((state) => ({
        parseHistory: [result, ...state.parseHistory.slice(0, 9)], // 保留最近10条
      }));

      // 更新统计信息
      get().updateStats(result);

      return result;
    } catch (error) {
      const appError: AppError = {
        code: 'AI_PROCESS_ERROR',
        message: error instanceof Error ? error.message : '图片处理失败',
        details: error,
      };

      set({
        isProcessing: false,
        isOCRProcessing: false,
        isParsingProcessing: false,
        error: appError,
      });

      throw appError;
    }
  },

  // 清除最后结果
  clearLastResult: () => {
    set({ lastParseResult: null });
  },

  // 清除历史记录
  clearHistory: () => {
    set({ parseHistory: [] });
  },

  // 设置错误
  setError: (error) => {
    set({ error, isProcessing: false });
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 开始截图监听
  startScreenshotListening: async () => {
    try {
      const state = get();
      
      // 设置截图处理回调
      ScreenshotService.addCallback((result: AIParseResult) => {
        set({
          lastParseResult: result,
          parseHistory: [result, ...state.parseHistory.slice(0, 9)],
        });
        get().updateStats(result);
      });

      // 开始监听
      await ScreenshotService.startListening(state.screenshotOptions);
      
      set({ 
        isScreenshotListening: true,
        error: null 
      });
    } catch (error) {
      const appError: AppError = {
        code: 'SCREENSHOT_LISTEN_ERROR',
        message: error instanceof Error ? error.message : '启动截图监听失败',
        details: error,
      };

      set({ 
        isScreenshotListening: false,
        error: appError 
      });
      
      throw appError;
    }
  },

  // 停止截图监听
  stopScreenshotListening: () => {
    ScreenshotService.stopListening();
    set({ isScreenshotListening: false });
  },

  // 更新截图选项
  updateScreenshotOptions: (options) => {
    set((state) => {
      const newOptions = { ...state.screenshotOptions, ...options };
      
      // 更新截图服务配置
      ScreenshotService.updateOptions(newOptions);
      
      return {
        screenshotOptions: newOptions,
      };
    });
  },

  // 更新统计信息
  updateStats: (result: AIParseResult) => {
    set((state) => {
      const newTotalProcessed = state.stats.totalProcessed + 1;
      const isSuccessful = result.confidence > 0.5;
      const newSuccessfulParses = state.stats.successfulParses + (isSuccessful ? 1 : 0);
      
      // 计算平均置信度
      const currentAverage = state.stats.averageConfidence;
      const newAverage = (currentAverage * state.stats.totalProcessed + result.confidence) / newTotalProcessed;

      return {
        stats: {
          totalProcessed: newTotalProcessed,
          successfulParses: newSuccessfulParses,
          averageConfidence: newAverage,
        },
      };
    });
  },

  // 重置统计信息
  resetStats: () => {
    set({
      stats: {
        totalProcessed: 0,
        successfulParses: 0,
        averageConfidence: 0,
      },
    });
  },
}));
