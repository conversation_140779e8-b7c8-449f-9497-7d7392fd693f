import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ColorPickerProps {
  label?: string;
  value: string;
  onSelect: (color: string) => void;
  required?: boolean;
  error?: string;
}

const COLOR_PALETTES = {
  '基础色': [
    '#FF3B30', '#FF9500', '#FFCC00', '#34C759', '#00C7BE',
    '#007AFF', '#5856D6', '#AF52DE', '#FF2D92', '#A2845E',
  ],
  '柔和色': [
    '#FFB3BA', '#FFDFBA', '#FFFFBA', '#BAFFC9', '#BAE1FF',
    '#C9C9FF', '#E1BAFF', '#FFBAF3', '#F0F0F0', '#D4D4D4',
  ],
  '深色系': [
    '#8B0000', '#B8860B', '#556B2F', '#2F4F4F', '#191970',
    '#4B0082', '#800080', '#8B008B', '#2F2F2F', '#1C1C1E',
  ],
  '商务色': [
    '#1F4E79', '#2E5984', '#3E6B8F', '#4E7D9A', '#5E8FA5',
    '#6EA1B0', '#7EB3BB', '#8EC5C6', '#9ED7D1', '#AEE9DC',
  ],
  '自然色': [
    '#8FBC8F', '#98FB98', '#90EE90', '#00FF7F', '#00FA9A',
    '#20B2AA', '#48D1CC', '#40E0D0', '#00CED1', '#5F9EA0',
  ],
  '暖色调': [
    '#FFA07A', '#FA8072', '#E9967A', '#F4A460', '#D2B48C',
    '#DEB887', '#F5DEB3', '#FFE4B5', '#FFDEAD', '#FFEBCD',
  ],
};

const ColorPicker: React.FC<ColorPickerProps> = ({
  label,
  value,
  onSelect,
  required = false,
  error,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [selectedPalette, setSelectedPalette] = useState('基础色');

  const handleSelect = (color: string) => {
    onSelect(color);
    setShowModal(false);
  };

  const renderColor = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={[
        styles.colorOption,
        { backgroundColor: item },
        value === item && styles.selectedColor,
      ]}
      onPress={() => handleSelect(item)}
    >
      {value === item && (
        <Ionicons name="checkmark" size={20} color="#FFFFFF" />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {label && (
        <Text style={styles.label}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <TouchableOpacity
        style={[styles.selector, error && styles.errorSelector]}
        onPress={() => setShowModal(true)}
      >
        <View style={styles.selectedValue}>
          <View style={[styles.colorPreview, { backgroundColor: value }]} />
          <Text style={styles.selectedText}>{value}</Text>
        </View>
        <Ionicons name="chevron-down" size={20} color="#8E8E93" />
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      <Modal
        visible={showModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>选择颜色</Text>
              <TouchableOpacity onPress={() => setShowModal(false)}>
                <Ionicons name="close" size={24} color="#8E8E93" />
              </TouchableOpacity>
            </View>

            <View style={styles.paletteSelector}>
              <FlatList
                horizontal
                data={Object.keys(COLOR_PALETTES)}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.paletteButton,
                      selectedPalette === item && styles.selectedPalette,
                    ]}
                    onPress={() => setSelectedPalette(item)}
                  >
                    <Text
                      style={[
                        styles.paletteText,
                        selectedPalette === item && styles.selectedPaletteText,
                      ]}
                    >
                      {item}
                    </Text>
                  </TouchableOpacity>
                )}
                keyExtractor={(item) => item}
                showsHorizontalScrollIndicator={false}
              />
            </View>

            <View style={styles.colorGrid}>
              <FlatList
                data={COLOR_PALETTES[selectedPalette as keyof typeof COLOR_PALETTES]}
                renderItem={renderColor}
                keyExtractor={(item) => item}
                numColumns={5}
                contentContainerStyle={styles.colorList}
              />
            </View>

            <View style={styles.currentSelection}>
              <Text style={styles.currentLabel}>当前选择:</Text>
              <View style={styles.currentColor}>
                <View style={[styles.currentColorPreview, { backgroundColor: value }]} />
                <Text style={styles.currentColorText}>{value}</Text>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  required: {
    color: '#FF3B30',
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  errorSelector: {
    borderColor: '#FF3B30',
  },
  selectedValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  colorPreview: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  selectedText: {
    fontSize: 16,
    color: '#1C1C1E',
    fontFamily: 'monospace',
  },
  errorText: {
    fontSize: 14,
    color: '#FF3B30',
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  paletteSelector: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  paletteButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
  },
  selectedPalette: {
    backgroundColor: '#007AFF',
  },
  paletteText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8E8E93',
  },
  selectedPaletteText: {
    color: '#FFFFFF',
  },
  colorGrid: {
    flex: 1,
    padding: 20,
  },
  colorList: {
    alignItems: 'center',
  },
  colorOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    margin: 8,
    borderWidth: 2,
    borderColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  selectedColor: {
    borderColor: '#1C1C1E',
    borderWidth: 3,
  },
  currentSelection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    backgroundColor: '#F8F9FA',
  },
  currentLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
  },
  currentColor: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  currentColorPreview: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  currentColorText: {
    fontSize: 16,
    color: '#1C1C1E',
    fontFamily: 'monospace',
    fontWeight: '500',
  },
});

export default ColorPicker;
