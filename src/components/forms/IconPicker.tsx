import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface IconOption {
  name: string;
  icon: keyof typeof Ionicons.glyphMap;
  category: string;
}

interface IconPickerProps {
  label?: string;
  value: string;
  onSelect: (icon: string) => void;
  required?: boolean;
  error?: string;
}

const ICON_CATEGORIES = {
  '餐饮': [
    { name: '餐厅', icon: 'restaurant' as const },
    { name: '咖啡', icon: 'cafe' as const },
    { name: '酒杯', icon: 'wine' as const },
    { name: '快餐', icon: 'fast-food' as const },
    { name: '冰淇淋', icon: 'ice-cream' as const },
    { name: '披萨', icon: 'pizza' as const },
  ],
  '交通': [
    { name: '汽车', icon: 'car' as const },
    { name: '公交', icon: 'bus' as const },
    { name: '地铁', icon: 'train' as const },
    { name: '飞机', icon: 'airplane' as const },
    { name: '自行车', icon: 'bicycle' as const },
    { name: '船', icon: 'boat' as const },
    { name: '加油', icon: 'car-sport' as const },
  ],
  '购物': [
    { name: '购物袋', icon: 'bag' as const },
    { name: '购物车', icon: 'cart' as const },
    { name: '礼物', icon: 'gift' as const },
    { name: '衣服', icon: 'shirt' as const },
    { name: '鞋子', icon: 'footsteps' as const },
    { name: '商店', icon: 'storefront' as const },
  ],
  '娱乐': [
    { name: '游戏', icon: 'game-controller' as const },
    { name: '电影', icon: 'film' as const },
    { name: '音乐', icon: 'musical-notes' as const },
    { name: '运动', icon: 'fitness' as const },
    { name: '相机', icon: 'camera' as const },
    { name: '书籍', icon: 'book' as const },
  ],
  '生活': [
    { name: '家', icon: 'home' as const },
    { name: '电话', icon: 'call' as const },
    { name: '网络', icon: 'wifi' as const },
    { name: '电力', icon: 'flash' as const },
    { name: '水', icon: 'water' as const },
    { name: '垃圾', icon: 'trash' as const },
  ],
  '医疗': [
    { name: '医院', icon: 'medical' as const },
    { name: '药品', icon: 'bandage' as const },
    { name: '牙医', icon: 'fitness' as const },
    { name: '眼镜', icon: 'glasses' as const },
    { name: '心脏', icon: 'heart' as const },
  ],
  '教育': [
    { name: '学校', icon: 'school' as const },
    { name: '图书馆', icon: 'library' as const },
    { name: '毕业帽', icon: 'school' as const },
    { name: '铅笔', icon: 'pencil' as const },
    { name: '计算器', icon: 'calculator' as const },
  ],
  '工作': [
    { name: '公文包', icon: 'briefcase' as const },
    { name: '电脑', icon: 'laptop' as const },
    { name: '文档', icon: 'document' as const },
    { name: '邮件', icon: 'mail' as const },
    { name: '日历', icon: 'calendar' as const },
  ],
  '金融': [
    { name: '银行卡', icon: 'card' as const },
    { name: '钱包', icon: 'wallet' as const },
    { name: '投资', icon: 'trending-up' as const },
    { name: '储蓄', icon: 'save' as const },
    { name: '奖杯', icon: 'trophy' as const },
    { name: '钻石', icon: 'diamond' as const },
  ],
  '其他': [
    { name: '标签', icon: 'pricetag' as const },
    { name: '星星', icon: 'star' as const },
    { name: '心形', icon: 'heart' as const },
    { name: '闪电', icon: 'flash' as const },
    { name: '更多', icon: 'ellipsis-horizontal' as const },
  ],
};

const IconPicker: React.FC<IconPickerProps> = ({
  label,
  value,
  onSelect,
  required = false,
  error,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('餐饮');

  const allIcons: IconOption[] = Object.entries(ICON_CATEGORIES).flatMap(
    ([category, icons]) =>
      icons.map(icon => ({
        ...icon,
        category,
      }))
  );

  const filteredIcons = searchText
    ? allIcons.filter(icon =>
        icon.name.toLowerCase().includes(searchText.toLowerCase())
      )
    : ICON_CATEGORIES[selectedCategory as keyof typeof ICON_CATEGORIES] || [];

  const selectedIcon = allIcons.find(icon => icon.icon === value);

  const handleSelect = (icon: string) => {
    onSelect(icon);
    setShowModal(false);
  };

  const renderIcon = ({ item }: { item: IconOption }) => (
    <TouchableOpacity
      style={[
        styles.iconOption,
        value === item.icon && styles.selectedIcon,
      ]}
      onPress={() => handleSelect(item.icon)}
    >
      <Ionicons name={item.icon} size={24} color="#1C1C1E" />
      <Text style={styles.iconName}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {label && (
        <Text style={styles.label}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <TouchableOpacity
        style={[styles.selector, error && styles.errorSelector]}
        onPress={() => setShowModal(true)}
      >
        <View style={styles.selectedValue}>
          {selectedIcon && (
            <Ionicons name={selectedIcon.icon} size={24} color="#1C1C1E" />
          )}
          <Text style={styles.selectedText}>
            {selectedIcon?.name || '选择图标'}
          </Text>
        </View>
        <Ionicons name="chevron-down" size={20} color="#8E8E93" />
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      <Modal
        visible={showModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>选择图标</Text>
              <TouchableOpacity onPress={() => setShowModal(false)}>
                <Ionicons name="close" size={24} color="#8E8E93" />
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color="#8E8E93" />
              <TextInput
                style={styles.searchInput}
                placeholder="搜索图标..."
                value={searchText}
                onChangeText={setSearchText}
              />
            </View>

            {!searchText && (
              <View style={styles.categorySelector}>
                <FlatList
                  horizontal
                  data={Object.keys(ICON_CATEGORIES)}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={[
                        styles.categoryButton,
                        selectedCategory === item && styles.selectedCategory,
                      ]}
                      onPress={() => setSelectedCategory(item)}
                    >
                      <Text
                        style={[
                          styles.categoryText,
                          selectedCategory === item && styles.selectedCategoryText,
                        ]}
                      >
                        {item}
                      </Text>
                    </TouchableOpacity>
                  )}
                  keyExtractor={(item) => item}
                  showsHorizontalScrollIndicator={false}
                />
              </View>
            )}

            <FlatList
              data={filteredIcons}
              renderItem={renderIcon}
              keyExtractor={(item) => item.icon}
              numColumns={4}
              contentContainerStyle={styles.iconGrid}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  required: {
    color: '#FF3B30',
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  errorSelector: {
    borderColor: '#FF3B30',
  },
  selectedValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  selectedText: {
    fontSize: 16,
    color: '#1C1C1E',
  },
  errorText: {
    fontSize: 14,
    color: '#FF3B30',
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#1C1C1E',
  },
  categorySelector: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
  },
  selectedCategory: {
    backgroundColor: '#007AFF',
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8E8E93',
  },
  selectedCategoryText: {
    color: '#FFFFFF',
  },
  iconGrid: {
    padding: 20,
  },
  iconOption: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    margin: 4,
    borderRadius: 8,
    backgroundColor: '#F8F9FA',
  },
  selectedIcon: {
    backgroundColor: '#E3F2FD',
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  iconName: {
    marginTop: 4,
    fontSize: 12,
    color: '#1C1C1E',
    textAlign: 'center',
  },
});

export default IconPicker;
