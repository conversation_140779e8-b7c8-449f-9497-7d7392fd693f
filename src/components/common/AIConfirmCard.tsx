import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { AIParseResult } from '../../types';
import Button from './Button';

interface AIConfirmCardProps {
  visible: boolean;
  parseResult: AIParseResult;
  onConfirm: (data: AIParseResult) => void;
  onCancel: () => void;
  onEdit: () => void;
}

const AIConfirmCard: React.FC<AIConfirmCardProps> = ({
  visible,
  parseResult,
  onConfirm,
  onCancel,
  onEdit,
}) => {
  const [editedResult, setEditedResult] = useState<AIParseResult>(parseResult);
  const [isEditing, setIsEditing] = useState(false);

  React.useEffect(() => {
    setEditedResult(parseResult);
    setIsEditing(false);
  }, [parseResult]);

  const handleConfirm = () => {
    if (editedResult.amount <= 0) {
      Alert.alert('错误', '金额必须大于0');
      return;
    }
    onConfirm(editedResult);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    setIsEditing(false);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return '#34C759';
    if (confidence >= 0.6) return '#FF9500';
    return '#FF3B30';
  };

  const getTypeIcon = (type: 'income' | 'expense') => {
    return type === 'income' ? 'arrow-down-circle' : 'arrow-up-circle';
  };

  const getTypeColor = (type: 'income' | 'expense') => {
    return type === 'income' ? '#34C759' : '#FF3B30';
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onCancel}
    >
      <View style={styles.overlay}>
        <View style={styles.card}>
          {/* 头部 */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Ionicons name="sparkles" size={24} color="#007AFF" />
              <Text style={styles.title}>AI识别结果</Text>
            </View>
            <TouchableOpacity onPress={onCancel} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#8E8E93" />
            </TouchableOpacity>
          </View>

          {/* 置信度指示器 */}
          <View style={styles.confidenceContainer}>
            <Text style={styles.confidenceLabel}>识别准确度</Text>
            <View style={styles.confidenceBar}>
              <View
                style={[
                  styles.confidenceFill,
                  {
                    width: `${editedResult.confidence * 100}%`,
                    backgroundColor: getConfidenceColor(editedResult.confidence),
                  },
                ]}
              />
            </View>
            <Text style={[
              styles.confidenceText,
              { color: getConfidenceColor(editedResult.confidence) }
            ]}>
              {Math.round(editedResult.confidence * 100)}%
            </Text>
          </View>

          {/* 交易信息 */}
          <View style={styles.content}>
            {/* 金额和类型 */}
            <View style={styles.amountContainer}>
              <Ionicons
                name={getTypeIcon(editedResult.type)}
                size={32}
                color={getTypeColor(editedResult.type)}
              />
              <View style={styles.amountInfo}>
                {isEditing ? (
                  <TextInput
                    style={styles.amountInput}
                    value={editedResult.amount.toString()}
                    onChangeText={(text) =>
                      setEditedResult({
                        ...editedResult,
                        amount: parseFloat(text) || 0,
                      })
                    }
                    keyboardType="numeric"
                    placeholder="输入金额"
                  />
                ) : (
                  <Text style={styles.amount}>¥{editedResult.amount.toFixed(2)}</Text>
                )}
                <Text style={[
                  styles.type,
                  { color: getTypeColor(editedResult.type) }
                ]}>
                  {editedResult.type === 'income' ? '收入' : '支出'}
                </Text>
              </View>
            </View>

            {/* 商家信息 */}
            {editedResult.merchant && (
              <View style={styles.infoRow}>
                <Text style={styles.label}>商家</Text>
                {isEditing ? (
                  <TextInput
                    style={styles.input}
                    value={editedResult.merchant}
                    onChangeText={(text) =>
                      setEditedResult({ ...editedResult, merchant: text })
                    }
                    placeholder="商家名称"
                  />
                ) : (
                  <Text style={styles.value}>{editedResult.merchant}</Text>
                )}
              </View>
            )}

            {/* 描述信息 */}
            {editedResult.description && (
              <View style={styles.infoRow}>
                <Text style={styles.label}>描述</Text>
                {isEditing ? (
                  <TextInput
                    style={styles.input}
                    value={editedResult.description}
                    onChangeText={(text) =>
                      setEditedResult({ ...editedResult, description: text })
                    }
                    placeholder="交易描述"
                  />
                ) : (
                  <Text style={styles.value}>{editedResult.description}</Text>
                )}
              </View>
            )}

            {/* 分类建议 */}
            {editedResult.category && (
              <View style={styles.infoRow}>
                <Text style={styles.label}>分类</Text>
                {isEditing ? (
                  <TextInput
                    style={styles.input}
                    value={editedResult.category}
                    onChangeText={(text) =>
                      setEditedResult({ ...editedResult, category: text })
                    }
                    placeholder="交易分类"
                  />
                ) : (
                  <Text style={styles.value}>{editedResult.category}</Text>
                )}
              </View>
            )}
          </View>

          {/* 操作按钮 */}
          <View style={styles.actions}>
            {isEditing ? (
              <>
                <Button
                  title="取消编辑"
                  variant="outline"
                  size="medium"
                  onPress={() => {
                    setEditedResult(parseResult);
                    setIsEditing(false);
                  }}
                  style={styles.actionButton}
                />
                <Button
                  title="保存"
                  variant="primary"
                  size="medium"
                  onPress={handleSaveEdit}
                  style={styles.actionButton}
                />
              </>
            ) : (
              <>
                <Button
                  title="编辑"
                  variant="outline"
                  size="medium"
                  onPress={handleEdit}
                  style={styles.actionButton}
                />
                <Button
                  title="确认记账"
                  variant="primary"
                  size="medium"
                  onPress={handleConfirm}
                  style={styles.actionButton}
                />
              </>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
    color: '#1C1C1E',
  },
  closeButton: {
    padding: 4,
  },
  confidenceContainer: {
    marginBottom: 20,
  },
  confidenceLabel: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 8,
  },
  confidenceBar: {
    height: 4,
    backgroundColor: '#E5E5EA',
    borderRadius: 2,
    marginBottom: 4,
  },
  confidenceFill: {
    height: '100%',
    borderRadius: 2,
  },
  confidenceText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'right',
  },
  content: {
    marginBottom: 24,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
  },
  amountInfo: {
    marginLeft: 12,
    flex: 1,
  },
  amount: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1C1C1E',
  },
  amountInput: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1C1C1E',
    borderBottomWidth: 1,
    borderBottomColor: '#007AFF',
    paddingBottom: 4,
  },
  type: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 4,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E5E5EA',
  },
  label: {
    fontSize: 16,
    color: '#8E8E93',
    fontWeight: '500',
  },
  value: {
    fontSize: 16,
    color: '#1C1C1E',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  input: {
    fontSize: 16,
    color: '#1C1C1E',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
    borderBottomWidth: 1,
    borderBottomColor: '#007AFF',
    paddingBottom: 2,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default AIConfirmCard;
