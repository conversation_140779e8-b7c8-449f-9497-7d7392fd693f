import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface BudgetItem {
  category_id: string;
  category_name: string;
  budget_amount: number;
  spent_amount: number;
  color: string;
  icon: string;
}

interface BudgetChartProps {
  data: BudgetItem[];
  title?: string;
  style?: any;
  onAddBudget?: () => void;
  onEditBudget?: (categoryId: string) => void;
}

const BudgetChart: React.FC<BudgetChartProps> = ({
  data,
  title = '预算管理',
  style,
  onAddBudget,
  onEditBudget,
}) => {
  const calculateProgress = (spent: number, budget: number) => {
    if (budget === 0) return 0;
    return Math.min((spent / budget) * 100, 100);
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 100) return '#FF3B30';
    if (progress >= 80) return '#FF9500';
    if (progress >= 60) return '#FFCC00';
    return '#34C759';
  };

  const getStatusIcon = (progress: number) => {
    if (progress >= 100) return 'warning';
    if (progress >= 80) return 'alert-circle';
    return 'checkmark-circle';
  };

  const totalBudget = data.reduce((sum, item) => sum + item.budget_amount, 0);
  const totalSpent = data.reduce((sum, item) => sum + item.spent_amount, 0);
  const overallProgress = calculateProgress(totalSpent, totalBudget);

  return (
    <View style={[styles.container, style]}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        
        {onAddBudget && (
          <TouchableOpacity style={styles.addButton} onPress={onAddBudget}>
            <Ionicons name="add" size={20} color="#007AFF" />
          </TouchableOpacity>
        )}
      </View>

      {/* 总体预算概览 */}
      <View style={styles.overallBudget}>
        <View style={styles.overallHeader}>
          <Text style={styles.overallTitle}>本月预算</Text>
          <View style={styles.overallStatus}>
            <Ionicons
              name={getStatusIcon(overallProgress)}
              size={16}
              color={getProgressColor(overallProgress)}
            />
            <Text style={[styles.overallProgress, { color: getProgressColor(overallProgress) }]}>
              {overallProgress.toFixed(0)}%
            </Text>
          </View>
        </View>
        
        <View style={styles.overallAmounts}>
          <Text style={styles.spentAmount}>
            已花费 ¥{totalSpent.toFixed(2)}
          </Text>
          <Text style={styles.budgetAmount}>
            / ¥{totalBudget.toFixed(2)}
          </Text>
        </View>
        
        <View style={styles.overallProgressBar}>
          <View
            style={[
              styles.overallProgressFill,
              {
                width: `${overallProgress}%`,
                backgroundColor: getProgressColor(overallProgress),
              },
            ]}
          />
        </View>
        
        <Text style={styles.remainingAmount}>
          剩余 ¥{Math.max(0, totalBudget - totalSpent).toFixed(2)}
        </Text>
      </View>

      {/* 分类预算列表 */}
      {data.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="wallet-outline" size={48} color="#C7C7CC" />
          <Text style={styles.emptyText}>暂无预算设置</Text>
          {onAddBudget && (
            <TouchableOpacity style={styles.emptyButton} onPress={onAddBudget}>
              <Text style={styles.emptyButtonText}>添加预算</Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <View style={styles.budgetList}>
          {data.map((item) => {
            const progress = calculateProgress(item.spent_amount, item.budget_amount);
            const progressColor = getProgressColor(progress);
            
            return (
              <TouchableOpacity
                key={item.category_id}
                style={styles.budgetItem}
                onPress={() => onEditBudget?.(item.category_id)}
              >
                <View style={styles.budgetItemHeader}>
                  <View style={styles.budgetItemLeft}>
                    <View style={[styles.categoryIcon, { backgroundColor: item.color + '20' }]}>
                      <Ionicons
                        name={item.icon as keyof typeof Ionicons.glyphMap}
                        size={20}
                        color={item.color}
                      />
                    </View>
                    <View style={styles.budgetItemInfo}>
                      <Text style={styles.categoryName}>{item.category_name}</Text>
                      <Text style={styles.budgetItemAmounts}>
                        ¥{item.spent_amount.toFixed(2)} / ¥{item.budget_amount.toFixed(2)}
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.budgetItemRight}>
                    <Ionicons
                      name={getStatusIcon(progress)}
                      size={16}
                      color={progressColor}
                    />
                    <Text style={[styles.budgetProgress, { color: progressColor }]}>
                      {progress.toFixed(0)}%
                    </Text>
                  </View>
                </View>
                
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      {
                        width: `${progress}%`,
                        backgroundColor: progressColor,
                      },
                    ]}
                  />
                </View>
                
                <Text style={styles.remainingText}>
                  剩余 ¥{Math.max(0, item.budget_amount - item.spent_amount).toFixed(2)}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F0F8FF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  overallBudget: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  overallHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  overallTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  overallStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  overallProgress: {
    fontSize: 14,
    fontWeight: '600',
  },
  overallAmounts: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  spentAmount: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1C1C1E',
  },
  budgetAmount: {
    fontSize: 16,
    color: '#8E8E93',
    marginLeft: 4,
  },
  overallProgressBar: {
    height: 8,
    backgroundColor: '#E5E5EA',
    borderRadius: 4,
    marginBottom: 8,
  },
  overallProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  remainingAmount: {
    fontSize: 14,
    color: '#8E8E93',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    color: '#8E8E93',
    marginBottom: 16,
  },
  emptyButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#007AFF',
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  budgetList: {
    gap: 16,
  },
  budgetItem: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
  },
  budgetItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  budgetItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  budgetItemInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  budgetItemAmounts: {
    fontSize: 14,
    color: '#8E8E93',
  },
  budgetItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  budgetProgress: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressBar: {
    height: 6,
    backgroundColor: '#E5E5EA',
    borderRadius: 3,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  remainingText: {
    fontSize: 12,
    color: '#8E8E93',
  },
});

export default BudgetChart;
