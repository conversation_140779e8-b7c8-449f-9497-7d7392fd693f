import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LineChart } from './LineChart';

interface TrendData {
  period: string;
  income: number;
  expense: number;
  net: number;
}

interface TrendChartProps {
  data: TrendData[];
  title?: string;
  style?: any;
  onPeriodChange?: (period: 'week' | 'month' | 'year') => void;
  selectedPeriod?: 'week' | 'month' | 'year';
}

const TrendChart: React.FC<TrendChartProps> = ({
  data,
  title = '收支趋势',
  style,
  onPeriodChange,
  selectedPeriod = 'month',
}) => {
  const [selectedType, setSelectedType] = React.useState<'income' | 'expense' | 'net'>('net');

  const getChartData = () => {
    const labels = data.map(item => item.period);
    let chartData: number[] = [];
    let color = '#007AFF';

    switch (selectedType) {
      case 'income':
        chartData = data.map(item => item.income);
        color = '#34C759';
        break;
      case 'expense':
        chartData = data.map(item => item.expense);
        color = '#FF3B30';
        break;
      case 'net':
        chartData = data.map(item => item.net);
        color = '#007AFF';
        break;
    }

    return {
      labels,
      datasets: [{
        data: chartData,
        color: (opacity = 1) => `rgba(${color === '#34C759' ? '52, 199, 89' : color === '#FF3B30' ? '255, 59, 48' : '0, 122, 255'}, ${opacity})`,
        strokeWidth: 2,
      }],
    };
  };

  const getTypeLabel = () => {
    switch (selectedType) {
      case 'income':
        return '收入';
      case 'expense':
        return '支出';
      case 'net':
        return '净收入';
    }
  };

  const getTypeIcon = () => {
    switch (selectedType) {
      case 'income':
        return 'arrow-down-circle';
      case 'expense':
        return 'arrow-up-circle';
      case 'net':
        return 'trending-up';
    }
  };

  const getTypeColor = () => {
    switch (selectedType) {
      case 'income':
        return '#34C759';
      case 'expense':
        return '#FF3B30';
      case 'net':
        return '#007AFF';
    }
  };

  const calculateTrend = () => {
    if (data.length < 2) return { trend: 'stable', percentage: 0 };

    const currentValue = data[data.length - 1][selectedType];
    const previousValue = data[data.length - 2][selectedType];
    
    if (previousValue === 0) return { trend: 'stable', percentage: 0 };

    const percentage = ((currentValue - previousValue) / Math.abs(previousValue)) * 100;
    
    if (percentage > 5) return { trend: 'up', percentage };
    if (percentage < -5) return { trend: 'down', percentage };
    return { trend: 'stable', percentage };
  };

  const trend = calculateTrend();

  const typeOptions = [
    { key: 'net' as const, label: '净收入', icon: 'trending-up', color: '#007AFF' },
    { key: 'income' as const, label: '收入', icon: 'arrow-down-circle', color: '#34C759' },
    { key: 'expense' as const, label: '支出', icon: 'arrow-up-circle', color: '#FF3B30' },
  ];

  return (
    <View style={[styles.container, style]}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        
        <View style={styles.trendIndicator}>
          <Ionicons
            name={getTypeIcon()}
            size={16}
            color={getTypeColor()}
          />
          <Text style={[styles.trendText, { color: getTypeColor() }]}>
            {getTypeLabel()}
          </Text>
          
          {trend.trend !== 'stable' && (
            <View style={styles.trendChange}>
              <Ionicons
                name={trend.trend === 'up' ? 'trending-up' : 'trending-down'}
                size={12}
                color={trend.trend === 'up' ? '#34C759' : '#FF3B30'}
              />
              <Text style={[
                styles.trendPercentage,
                { color: trend.trend === 'up' ? '#34C759' : '#FF3B30' }
              ]}>
                {Math.abs(trend.percentage).toFixed(1)}%
              </Text>
            </View>
          )}
        </View>
      </View>

      <View style={styles.typeSelector}>
        {typeOptions.map((option) => (
          <TouchableOpacity
            key={option.key}
            style={[
              styles.typeButton,
              selectedType === option.key && [styles.selectedTypeButton, { backgroundColor: option.color + '20' }],
            ]}
            onPress={() => setSelectedType(option.key)}
          >
            <Ionicons
              name={option.icon as keyof typeof Ionicons.glyphMap}
              size={16}
              color={selectedType === option.key ? option.color : '#8E8E93'}
            />
            <Text
              style={[
                styles.typeButtonText,
                selectedType === option.key && { color: option.color },
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <LineChart
        data={getChartData()}
        showGrid={true}
        showDots={true}
        bezier={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  trendIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  trendText: {
    fontSize: 14,
    fontWeight: '500',
  },
  trendChange: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
    marginLeft: 4,
  },
  trendPercentage: {
    fontSize: 12,
    fontWeight: '500',
  },
  typeSelector: {
    flexDirection: 'row',
    marginBottom: 16,
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
    padding: 2,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 4,
  },
  selectedTypeButton: {
    backgroundColor: '#007AFF20',
  },
  typeButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#8E8E93',
  },
});

export default TrendChart;
