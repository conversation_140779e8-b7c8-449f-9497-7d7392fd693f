import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { LineChart as RNLine<PERSON>hart } from 'react-native-chart-kit';

const { width: screenWidth } = Dimensions.get('window');

interface LineChartData {
  labels: string[];
  datasets: {
    data: number[];
    color?: (opacity: number) => string;
    strokeWidth?: number;
  }[];
}

interface LineChartProps {
  data: LineChartData;
  title?: string;
  style?: any;
  showGrid?: boolean;
  showDots?: boolean;
  bezier?: boolean;
}

const LineChart: React.FC<LineChartProps> = ({
  data,
  title,
  style,
  showGrid = true,
  showDots = true,
  bezier = false,
}) => {
  const chartConfig = {
    backgroundGradientFrom: '#ffffff',
    backgroundGradientTo: '#ffffff',
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(0, 122, 255, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(142, 142, 147, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: showDots ? '4' : '0',
      strokeWidth: '2',
      stroke: '#007AFF',
    },
    propsForBackgroundLines: {
      strokeDasharray: showGrid ? '' : '0',
      stroke: showGrid ? '#E5E5EA' : 'transparent',
    },
  };

  if (!data.datasets || data.datasets.length === 0 || data.datasets[0].data.length === 0) {
    return (
      <View style={[styles.container, style]}>
        {title && <Text style={styles.title}>{title}</Text>}
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>暂无数据</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {title && <Text style={styles.title}>{title}</Text>}
      
      <View style={styles.chartContainer}>
        <RNLineChart
          data={data}
          width={screenWidth - 80}
          height={220}
          chartConfig={chartConfig}
          bezier={bezier}
          style={styles.chart}
          withDots={showDots}
          withShadow={false}
          withVerticalLabels={true}
          withHorizontalLabels={true}
          withInnerLines={showGrid}
          withOuterLines={showGrid}
          yAxisLabel="¥"
          yAxisSuffix=""
          formatYLabel={(value) => {
            const num = parseFloat(value);
            if (num >= 10000) {
              return `${(num / 10000).toFixed(1)}万`;
            } else if (num >= 1000) {
              return `${(num / 1000).toFixed(1)}k`;
            }
            return num.toString();
          }}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
    textAlign: 'center',
  },
  chartContainer: {
    alignItems: 'center',
  },
  chart: {
    borderRadius: 16,
  },
  emptyContainer: {
    height: 220,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#8E8E93',
  },
});

export default LineChart;
