import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { TextInput, Picker, DatePicker, PickerOption } from '../forms';
import { Button } from '../common';

export interface AdvancedSearchFilters {
  keyword?: string;
  type?: 'income' | 'expense' | '';
  category_id?: string;
  account_id?: string;
  amount_min?: number;
  amount_max?: number;
  date_start?: Date;
  date_end?: Date;
  tags?: string[];
  is_ai_generated?: boolean | null;
}

interface AdvancedSearchProps {
  visible: boolean;
  onClose: () => void;
  onSearch: (filters: AdvancedSearchFilters) => void;
  onReset: () => void;
  initialFilters?: AdvancedSearchFilters;
  categories: Array<{ id: string; name: string; type: string }>;
  accounts: Array<{ id: string; name: string }>;
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  visible,
  onClose,
  onSearch,
  onReset,
  initialFilters = {},
  categories,
  accounts,
}) => {
  const [filters, setFilters] = useState<AdvancedSearchFilters>(initialFilters);

  const typeOptions: PickerOption[] = [
    { label: '全部类型', value: '' },
    { label: '收入', value: 'income', icon: 'arrow-down-circle', color: '#34C759' },
    { label: '支出', value: 'expense', icon: 'arrow-up-circle', color: '#FF3B30' },
  ];

  const categoryOptions: PickerOption[] = [
    { label: '全部分类', value: '' },
    ...categories.map(category => ({
      label: category.name,
      value: category.id,
    })),
  ];

  const accountOptions: PickerOption[] = [
    { label: '全部账户', value: '' },
    ...accounts.map(account => ({
      label: account.name,
      value: account.id,
    })),
  ];

  const aiGeneratedOptions: PickerOption[] = [
    { label: '全部记录', value: '' },
    { label: 'AI识别', value: 'true', icon: 'sparkles', color: '#007AFF' },
    { label: '手动输入', value: 'false', icon: 'create', color: '#8E8E93' },
  ];

  const handleSearch = () => {
    onSearch(filters);
    onClose();
  };

  const handleReset = () => {
    setFilters({});
    onReset();
  };

  const updateFilter = (key: keyof AdvancedSearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.keyword) count++;
    if (filters.type) count++;
    if (filters.category_id) count++;
    if (filters.account_id) count++;
    if (filters.amount_min !== undefined || filters.amount_max !== undefined) count++;
    if (filters.date_start || filters.date_end) count++;
    if (filters.tags && filters.tags.length > 0) count++;
    if (filters.is_ai_generated !== null && filters.is_ai_generated !== undefined) count++;
    return count;
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>高级搜索</Text>
            <View style={styles.headerRight}>
              {getActiveFiltersCount() > 0 && (
                <View style={styles.filterBadge}>
                  <Text style={styles.filterBadgeText}>
                    {getActiveFiltersCount()}
                  </Text>
                </View>
              )}
              <TouchableOpacity onPress={onClose}>
                <Ionicons name="close" size={24} color="#8E8E93" />
              </TouchableOpacity>
            </View>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* 关键词搜索 */}
            <TextInput
              label="关键词"
              value={filters.keyword || ''}
              onChangeText={(text) => updateFilter('keyword', text)}
              placeholder="搜索描述、备注、商家等"
              leftIcon="search-outline"
            />

            {/* 交易类型 */}
            <Picker
              label="交易类型"
              value={filters.type || ''}
              options={typeOptions}
              onSelect={(option) => updateFilter('type', option.value)}
            />

            {/* 分类筛选 */}
            <Picker
              label="分类"
              value={filters.category_id || ''}
              options={categoryOptions}
              onSelect={(option) => updateFilter('category_id', option.value)}
            />

            {/* 账户筛选 */}
            <Picker
              label="账户"
              value={filters.account_id || ''}
              options={accountOptions}
              onSelect={(option) => updateFilter('account_id', option.value)}
            />

            {/* 金额范围 */}
            <View style={styles.amountRange}>
              <Text style={styles.sectionLabel}>金额范围</Text>
              <View style={styles.amountInputs}>
                <TextInput
                  value={filters.amount_min?.toString() || ''}
                  onChangeText={(text) => {
                    const value = text ? parseFloat(text) : undefined;
                    updateFilter('amount_min', value);
                  }}
                  placeholder="最小金额"
                  keyboardType="decimal-pad"
                  containerStyle={styles.amountInput}
                />
                <Text style={styles.amountSeparator}>至</Text>
                <TextInput
                  value={filters.amount_max?.toString() || ''}
                  onChangeText={(text) => {
                    const value = text ? parseFloat(text) : undefined;
                    updateFilter('amount_max', value);
                  }}
                  placeholder="最大金额"
                  keyboardType="decimal-pad"
                  containerStyle={styles.amountInput}
                />
              </View>
            </View>

            {/* 日期范围 */}
            <View style={styles.dateRange}>
              <Text style={styles.sectionLabel}>日期范围</Text>
              <View style={styles.dateInputs}>
                <DatePicker
                  label="开始日期"
                  value={filters.date_start}
                  onChange={(date) => updateFilter('date_start', date)}
                  placeholder="选择开始日期"
                />
                <DatePicker
                  label="结束日期"
                  value={filters.date_end}
                  onChange={(date) => updateFilter('date_end', date)}
                  placeholder="选择结束日期"
                  minimumDate={filters.date_start}
                />
              </View>
            </View>

            {/* AI识别筛选 */}
            <Picker
              label="记录来源"
              value={filters.is_ai_generated === null || filters.is_ai_generated === undefined ? '' : filters.is_ai_generated.toString()}
              options={aiGeneratedOptions}
              onSelect={(option) => {
                const value = option.value === '' ? null : option.value === 'true';
                updateFilter('is_ai_generated', value);
              }}
            />

            {/* 标签筛选 */}
            <View style={styles.tagsSection}>
              <Text style={styles.sectionLabel}>标签</Text>
              <TouchableOpacity
                style={styles.tagsButton}
                onPress={() => {
                  // TODO: 打开标签选择器
                }}
              >
                <Text style={styles.tagsButtonText}>
                  {filters.tags && filters.tags.length > 0
                    ? `已选择 ${filters.tags.length} 个标签`
                    : '选择标签'}
                </Text>
                <Ionicons name="chevron-forward" size={16} color="#C7C7CC" />
              </TouchableOpacity>
            </View>
          </ScrollView>

          {/* 底部按钮 */}
          <View style={styles.footer}>
            <Button
              title="重置"
              variant="outline"
              onPress={handleReset}
              style={styles.footerButton}
            />
            <Button
              title="搜索"
              onPress={handleSearch}
              style={styles.footerButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '90%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  filterBadge: {
    backgroundColor: '#FF3B30',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterBadgeText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  content: {
    padding: 20,
    maxHeight: 500,
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  amountRange: {
    marginBottom: 16,
  },
  amountInputs: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  amountInput: {
    flex: 1,
    marginBottom: 0,
  },
  amountSeparator: {
    fontSize: 16,
    color: '#8E8E93',
    paddingHorizontal: 8,
  },
  dateRange: {
    marginBottom: 16,
  },
  dateInputs: {
    gap: 12,
  },
  tagsSection: {
    marginBottom: 16,
  },
  tagsButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  tagsButtonText: {
    fontSize: 16,
    color: '#1C1C1E',
  },
  footer: {
    flexDirection: 'row',
    gap: 12,
    padding: 20,
    paddingBottom: 34,
  },
  footerButton: {
    flex: 1,
  },
});

export default AdvancedSearch;
