import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface BatchOperationBarProps {
  selectedCount: number;
  onSelectAll: () => void;
  onDeselectAll: () => void;
  onBatchDelete: () => void;
  onBatchEdit: () => void;
  onBatchExport: () => void;
  onCancel: () => void;
  totalCount: number;
  isAllSelected: boolean;
}

const BatchOperationBar: React.FC<BatchOperationBarProps> = ({
  selectedCount,
  onSelectAll,
  onDeselectAll,
  onBatchDelete,
  onBatchEdit,
  onBatchExport,
  onCancel,
  totalCount,
  isAllSelected,
}) => {
  const handleBatchDelete = () => {
    Alert.alert(
      '确认删除',
      `确定要删除选中的 ${selectedCount} 条交易记录吗？此操作无法撤销。`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: onBatchDelete,
        },
      ]
    );
  };

  const handleBatchEdit = () => {
    Alert.alert(
      '批量编辑',
      '选择要批量修改的属性',
      [
        { text: '取消', style: 'cancel' },
        { text: '修改分类', onPress: () => onBatchEdit() },
        { text: '修改账户', onPress: () => onBatchEdit() },
        { text: '添加标签', onPress: () => onBatchEdit() },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.leftSection}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={onCancel}
        >
          <Ionicons name="close" size={20} color="#8E8E93" />
        </TouchableOpacity>
        
        <Text style={styles.selectedText}>
          已选择 {selectedCount} 项
        </Text>
        
        <TouchableOpacity
          style={styles.selectAllButton}
          onPress={isAllSelected ? onDeselectAll : onSelectAll}
        >
          <Text style={styles.selectAllText}>
            {isAllSelected ? '取消全选' : `全选(${totalCount})`}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.rightSection}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={onBatchExport}
          disabled={selectedCount === 0}
        >
          <Ionicons 
            name="download-outline" 
            size={20} 
            color={selectedCount > 0 ? "#007AFF" : "#C7C7CC"} 
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleBatchEdit}
          disabled={selectedCount === 0}
        >
          <Ionicons 
            name="create-outline" 
            size={20} 
            color={selectedCount > 0 ? "#007AFF" : "#C7C7CC"} 
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleBatchDelete}
          disabled={selectedCount === 0}
        >
          <Ionicons 
            name="trash-outline" 
            size={20} 
            color={selectedCount > 0 ? "#FF3B30" : "#C7C7CC"} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  cancelButton: {
    padding: 4,
    marginRight: 12,
  },
  selectedText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginRight: 16,
  },
  selectAllButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  selectAllText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default BatchOperationBar;
