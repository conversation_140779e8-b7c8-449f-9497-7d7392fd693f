import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Modal,
  FlatList,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface TagManagerProps {
  visible: boolean;
  onClose: () => void;
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  availableTags?: string[];
}

const TagManager: React.FC<TagManagerProps> = ({
  visible,
  onClose,
  selectedTags,
  onTagsChange,
  availableTags = [],
}) => {
  const [newTagText, setNewTagText] = useState('');
  const [allTags, setAllTags] = useState<string[]>([
    '餐饮',
    '交通',
    '购物',
    '娱乐',
    '医疗',
    '教育',
    '旅游',
    '工作',
    '家庭',
    '投资',
    ...availableTags,
  ]);

  const handleAddTag = () => {
    const trimmedTag = newTagText.trim();
    if (!trimmedTag) {
      Alert.alert('提示', '请输入标签名称');
      return;
    }

    if (allTags.includes(trimmedTag)) {
      Alert.alert('提示', '标签已存在');
      return;
    }

    setAllTags(prev => [...prev, trimmedTag]);
    setNewTagText('');
  };

  const handleToggleTag = (tag: string) => {
    const isSelected = selectedTags.includes(tag);
    let newSelectedTags: string[];

    if (isSelected) {
      newSelectedTags = selectedTags.filter(t => t !== tag);
    } else {
      newSelectedTags = [...selectedTags, tag];
    }

    onTagsChange(newSelectedTags);
  };

  const handleRemoveTag = (tag: string) => {
    Alert.alert(
      '确认删除',
      `确定要删除标签"${tag}"吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => {
            setAllTags(prev => prev.filter(t => t !== tag));
            onTagsChange(selectedTags.filter(t => t !== tag));
          },
        },
      ]
    );
  };

  const renderTag = ({ item }: { item: string }) => {
    const isSelected = selectedTags.includes(item);
    
    return (
      <View style={styles.tagItem}>
        <TouchableOpacity
          style={[
            styles.tagButton,
            isSelected && styles.selectedTagButton,
          ]}
          onPress={() => handleToggleTag(item)}
        >
          <Text
            style={[
              styles.tagText,
              isSelected && styles.selectedTagText,
            ]}
          >
            {item}
          </Text>
          {isSelected && (
            <Ionicons name="checkmark" size={16} color="#FFFFFF" />
          )}
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.deleteTagButton}
          onPress={() => handleRemoveTag(item)}
        >
          <Ionicons name="close" size={14} color="#8E8E93" />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>标签管理</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#8E8E93" />
            </TouchableOpacity>
          </View>

          {/* 添加新标签 */}
          <View style={styles.addTagSection}>
            <View style={styles.addTagInput}>
              <TextInput
                style={styles.textInput}
                value={newTagText}
                onChangeText={setNewTagText}
                placeholder="输入新标签名称"
                maxLength={20}
                returnKeyType="done"
                onSubmitEditing={handleAddTag}
              />
              <TouchableOpacity
                style={styles.addButton}
                onPress={handleAddTag}
                disabled={!newTagText.trim()}
              >
                <Ionicons 
                  name="add" 
                  size={20} 
                  color={newTagText.trim() ? "#007AFF" : "#C7C7CC"} 
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* 已选择的标签 */}
          {selectedTags.length > 0 && (
            <View style={styles.selectedSection}>
              <Text style={styles.sectionTitle}>
                已选择 ({selectedTags.length})
              </Text>
              <View style={styles.selectedTagsContainer}>
                {selectedTags.map((tag, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.selectedTag}
                    onPress={() => handleToggleTag(tag)}
                  >
                    <Text style={styles.selectedTagText}>{tag}</Text>
                    <Ionicons name="close" size={14} color="#FFFFFF" />
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          {/* 所有标签列表 */}
          <View style={styles.allTagsSection}>
            <Text style={styles.sectionTitle}>
              所有标签 ({allTags.length})
            </Text>
            <FlatList
              data={allTags}
              renderItem={renderTag}
              keyExtractor={(item) => item}
              numColumns={2}
              columnWrapperStyle={styles.tagRow}
              showsVerticalScrollIndicator={false}
              style={styles.tagsList}
            />
          </View>

          {/* 底部按钮 */}
          <View style={styles.footer}>
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => onTagsChange([])}
            >
              <Text style={styles.clearButtonText}>清空选择</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={onClose}
            >
              <Text style={styles.confirmButtonText}>确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  addTagSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  addTagInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 12,
    color: '#1C1C1E',
  },
  addButton: {
    padding: 8,
  },
  selectedSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 12,
  },
  selectedTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  selectedTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  selectedTagText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  allTagsSection: {
    padding: 20,
    flex: 1,
  },
  tagsList: {
    maxHeight: 300,
  },
  tagRow: {
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  tagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '48%',
  },
  tagButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F2F2F7',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 4,
  },
  selectedTagButton: {
    backgroundColor: '#007AFF',
  },
  tagText: {
    fontSize: 14,
    color: '#1C1C1E',
    fontWeight: '500',
  },
  deleteTagButton: {
    padding: 4,
  },
  footer: {
    flexDirection: 'row',
    gap: 12,
    padding: 20,
    paddingBottom: 34,
  },
  clearButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: 16,
    color: '#8E8E93',
    fontWeight: '500',
  },
  confirmButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#007AFF',
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});

export default TagManager;
