// 基础数据类型定义

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Account {
  id: string;
  user_id: string;
  name: string;
  type: 'cash' | 'bank' | 'alipay' | 'wechat' | 'credit_card' | 'other';
  balance: number;
  currency: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  user_id: string;
  name: string;
  type: 'income' | 'expense';
  icon: string;
  color: string;
  parent_id?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  user_id: string;
  account_id: string;
  category_id: string;
  amount: number;
  type: 'income' | 'expense';
  description?: string;
  notes?: string;
  transaction_date: string;
  location?: string;
  tags?: string[];
  receipt_url?: string;
  is_ai_generated: boolean;
  ai_confidence?: number;
  created_at: string;
  updated_at: string;

  // 关联数据
  account?: Account;
  category?: Category;
}

// AI相关类型
export interface AIParseResult {
  amount: number;
  type: 'income' | 'expense';
  merchant?: string;
  description?: string;
  category?: string;
  confidence: number;
  raw_text?: string;
  needs_manual_input?: boolean;
}

export interface OCRResult {
  text: string;
  confidence: number;
  bounding_boxes?: Array<{
    text: string;
    x: number;
    y: number;
    width: number;
    height: number;
  }>;
}

// 统计相关类型
export interface MonthlyStats {
  month: string;
  total_income: number;
  total_expense: number;
  net_amount: number;
  transaction_count: number;
  top_categories: Array<{
    category_id: string;
    category_name: string;
    amount: number;
    percentage: number;
  }>;
}

export interface CategoryStats {
  category_id: string;
  category_name: string;
  total_amount: number;
  transaction_count: number;
  percentage: number;
  trend: 'up' | 'down' | 'stable';
}

export interface Budget {
  id: string;
  user_id: string;
  category_id: string;
  amount: number;
  period: 'weekly' | 'monthly' | 'yearly';
  start_date: string;
  end_date: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;

  // 关联数据
  category?: Category;
  spent_amount?: number;
}

export interface TrendData {
  period: string;
  income: number;
  expense: number;
  net: number;
  transaction_count: number;
}

export interface BudgetStats {
  category_id: string;
  category_name: string;
  budget_amount: number;
  spent_amount: number;
  remaining_amount: number;
  progress_percentage: number;
  status: 'safe' | 'warning' | 'exceeded';
  color: string;
  icon: string;
}

// 导航相关类型
export type RootStackParamList = {
  Home: undefined;
  AddTransaction: {
    aiData?: AIParseResult;
    editTransaction?: Transaction;
  };
  AICapture: undefined;
  TransactionList: {
    filter?: {
      type?: 'income' | 'expense';
      category_id?: string;
      account_id?: string;
      date_range?: {
        start: string;
        end: string;
      };
    };
  };
  Settings: undefined;
  TransactionDetail: { transactionId: string };
  AccountManagement: undefined;
  CategoryManagement: undefined;
};

export type TabParamList = {
  HomeTab: undefined;
  TransactionsTab: undefined;
  AITab: undefined;
  StatsTab: undefined;
  SettingsTab: undefined;
};

// 表单相关类型
export interface TransactionFormData {
  amount: string;
  type: 'income' | 'expense';
  category_id: string;
  account_id: string;
  description?: string;
  notes?: string;
  transaction_date: Date;
  tags?: string[];
}

export interface AccountFormData {
  name: string;
  type: Account['type'];
  balance: string;
  currency: string;
}

export interface CategoryFormData {
  name: string;
  type: 'income' | 'expense';
  icon: string;
  color: string;
  parent_id?: string;
}

// API响应类型
export interface ApiResponse<T> {
  data: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  has_more: boolean;
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// 应用状态类型
export interface AppState {
  isLoading: boolean;
  error?: AppError;
  user?: User;
  isAuthenticated: boolean;
}

export interface TransactionState {
  transactions: Transaction[];
  isLoading: boolean;
  error?: AppError;
  filter: {
    type?: 'income' | 'expense';
    category_id?: string;
    account_id?: string;
    date_range?: {
      start: string;
      end: string;
    };
  };
}

export interface AIState {
  isProcessing: boolean;
  lastParseResult?: AIParseResult;
  error?: AppError;
  isScreenshotListening: boolean;
}
