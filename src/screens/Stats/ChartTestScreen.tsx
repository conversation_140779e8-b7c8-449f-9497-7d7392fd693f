import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '../../components/charts';

const ChartTestScreen: React.FC = () => {
  // 测试数据
  const pieData = [
    { name: '餐饮', amount: 1200, color: '#FF3B30' },
    { name: '交通', amount: 800, color: '#007AFF' },
    { name: '购物', amount: 1500, color: '#34C759' },
    { name: '娱乐', amount: 600, color: '#FF9500' },
  ];

  const lineData = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [{
      data: [2000, 2500, 1800, 3200, 2800, 3500],
    }],
  };

  const barData = {
    labels: ['周一', '周二', '周三', '周四', '周五'],
    datasets: [{
      data: [200, 450, 280, 800, 990],
    }],
  };

  const trendData = [
    { period: '1月', income: 5000, expense: 3000, net: 2000, transaction_count: 25 },
    { period: '2月', income: 5500, expense: 3200, net: 2300, transaction_count: 28 },
    { period: '3月', income: 4800, expense: 2800, net: 2000, transaction_count: 22 },
    { period: '4月', income: 6000, expense: 3500, net: 2500, transaction_count: 30 },
    { period: '5月', income: 5200, expense: 3100, net: 2100, transaction_count: 26 },
    { period: '6月', income: 5800, expense: 3400, net: 2400, transaction_count: 29 },
  ];

  const budgetData = [
    {
      category_id: 'food',
      category_name: '餐饮',
      budget_amount: 2000,
      spent_amount: 1800,
      remaining_amount: 200,
      progress_percentage: 90,
      status: 'warning' as const,
      color: '#FF3B30',
      icon: 'restaurant',
    },
    {
      category_id: 'transport',
      category_name: '交通',
      budget_amount: 800,
      spent_amount: 600,
      remaining_amount: 200,
      progress_percentage: 75,
      status: 'safe' as const,
      color: '#007AFF',
      icon: 'car',
    },
    {
      category_id: 'shopping',
      category_name: '购物',
      budget_amount: 1500,
      spent_amount: 1600,
      remaining_amount: 0,
      progress_percentage: 107,
      status: 'exceeded' as const,
      color: '#34C759',
      icon: 'bag',
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>图表组件测试</Text>
      
      <View style={styles.section}>
        <PieChart
          data={pieData}
          title="饼图测试"
          style={styles.chart}
        />
      </View>

      <View style={styles.section}>
        <LineChart
          data={lineData}
          title="折线图测试"
          style={styles.chart}
        />
      </View>

      <View style={styles.section}>
        <BarChart
          data={barData}
          title="柱状图测试"
          style={styles.chart}
        />
      </View>

      <View style={styles.section}>
        <TrendChart
          data={trendData}
          title="趋势图测试"
          style={styles.chart}
        />
      </View>

      <View style={styles.section}>
        <BudgetChart
          data={budgetData}
          title="预算图测试"
          style={styles.chart}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#1C1C1E',
  },
  section: {
    marginBottom: 20,
  },
  chart: {
    marginBottom: 16,
  },
});

export default ChartTestScreen;
