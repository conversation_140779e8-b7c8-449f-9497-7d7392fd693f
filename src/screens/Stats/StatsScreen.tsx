import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Card, LoadingSpinner, EmptyState } from '../../components/common';
import { useAppStore } from '../../stores/appStore';
import { useTransactionStore } from '../../stores/transactionStore';
import { MonthlyStats, CategoryStats } from '../../types';

const { width: screenWidth } = Dimensions.get('window');

const StatsScreen: React.FC = () => {
  const { user } = useAppStore();
  const { transactions, accounts, categories, loadTransactions, loadAccounts, loadCategories } = useTransactionStore();

  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      await Promise.all([
        loadTransactions(user.id, true),
        loadAccounts(user.id),
        loadCategories(user.id),
      ]);
    } catch (error) {
      console.error('Failed to load stats data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 计算统计数据
  const calculateStats = () => {
    const now = new Date();
    let startDate: Date;

    switch (selectedPeriod) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
    }

    const filteredTransactions = transactions.filter(t => 
      new Date(t.transaction_date) >= startDate
    );

    const totalIncome = filteredTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpense = filteredTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    const netAmount = totalIncome - totalExpense;

    // 按分类统计
    const categoryStats: { [key: string]: CategoryStats } = {};
    
    filteredTransactions.forEach(transaction => {
      const categoryId = transaction.category_id;
      const category = categories.find(c => c.id === categoryId);
      
      if (!categoryStats[categoryId]) {
        categoryStats[categoryId] = {
          category_id: categoryId,
          category_name: category?.name || '未知分类',
          total_amount: 0,
          transaction_count: 0,
          percentage: 0,
          trend: 'stable',
        };
      }
      
      categoryStats[categoryId].total_amount += transaction.amount;
      categoryStats[categoryId].transaction_count += 1;
    });

    // 计算百分比
    const totalAmount = totalIncome + totalExpense;
    Object.values(categoryStats).forEach(stat => {
      stat.percentage = totalAmount > 0 ? (stat.total_amount / totalAmount) * 100 : 0;
    });

    const topCategories = Object.values(categoryStats)
      .sort((a, b) => b.total_amount - a.total_amount)
      .slice(0, 5);

    return {
      totalIncome,
      totalExpense,
      netAmount,
      transactionCount: filteredTransactions.length,
      topCategories,
    };
  };

  const stats = calculateStats();

  const periodOptions = [
    { label: '本周', value: 'week' as const },
    { label: '本月', value: 'month' as const },
    { label: '本年', value: 'year' as const },
  ];

  const renderOverviewCard = () => (
    <Card style={styles.overviewCard}>
      <Text style={styles.cardTitle}>财务概览</Text>
      
      <View style={styles.overviewGrid}>
        <View style={styles.overviewItem}>
          <View style={[styles.overviewIcon, { backgroundColor: '#34C759' + '20' }]}>
            <Ionicons name="arrow-down-circle" size={24} color="#34C759" />
          </View>
          <Text style={styles.overviewLabel}>收入</Text>
          <Text style={[styles.overviewAmount, { color: '#34C759' }]}>
            ¥{stats.totalIncome.toFixed(2)}
          </Text>
        </View>

        <View style={styles.overviewItem}>
          <View style={[styles.overviewIcon, { backgroundColor: '#FF3B30' + '20' }]}>
            <Ionicons name="arrow-up-circle" size={24} color="#FF3B30" />
          </View>
          <Text style={styles.overviewLabel}>支出</Text>
          <Text style={[styles.overviewAmount, { color: '#FF3B30' }]}>
            ¥{stats.totalExpense.toFixed(2)}
          </Text>
        </View>

        <View style={styles.overviewItem}>
          <View style={[styles.overviewIcon, { backgroundColor: '#007AFF' + '20' }]}>
            <Ionicons name="trending-up" size={24} color="#007AFF" />
          </View>
          <Text style={styles.overviewLabel}>净收入</Text>
          <Text style={[
            styles.overviewAmount,
            { color: stats.netAmount >= 0 ? '#34C759' : '#FF3B30' }
          ]}>
            ¥{stats.netAmount.toFixed(2)}
          </Text>
        </View>

        <View style={styles.overviewItem}>
          <View style={[styles.overviewIcon, { backgroundColor: '#8E8E93' + '20' }]}>
            <Ionicons name="receipt" size={24} color="#8E8E93" />
          </View>
          <Text style={styles.overviewLabel}>交易笔数</Text>
          <Text style={styles.overviewAmount}>
            {stats.transactionCount}
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderCategoryChart = () => (
    <Card style={styles.chartCard}>
      <Text style={styles.cardTitle}>分类统计</Text>
      
      {stats.topCategories.length === 0 ? (
        <View style={styles.emptyChart}>
          <Ionicons name="pie-chart-outline" size={48} color="#C7C7CC" />
          <Text style={styles.emptyChartText}>暂无数据</Text>
        </View>
      ) : (
        <View style={styles.categoryList}>
          {stats.topCategories.map((category, index) => {
            const categoryInfo = categories.find(c => c.id === category.category_id);
            return (
              <View key={category.category_id} style={styles.categoryItem}>
                <View style={styles.categoryLeft}>
                  <View style={[
                    styles.categoryIcon,
                    { backgroundColor: categoryInfo?.color + '20' || '#8E8E93' + '20' }
                  ]}>
                    <Ionicons
                      name={categoryInfo?.icon as keyof typeof Ionicons.glyphMap || 'pricetag'}
                      size={20}
                      color={categoryInfo?.color || '#8E8E93'}
                    />
                  </View>
                  <View style={styles.categoryInfo}>
                    <Text style={styles.categoryName}>{category.category_name}</Text>
                    <Text style={styles.categoryCount}>
                      {category.transaction_count} 笔交易
                    </Text>
                  </View>
                </View>
                
                <View style={styles.categoryRight}>
                  <Text style={styles.categoryAmount}>
                    ¥{category.total_amount.toFixed(2)}
                  </Text>
                  <Text style={styles.categoryPercentage}>
                    {category.percentage.toFixed(1)}%
                  </Text>
                </View>
              </View>
            );
          })}
        </View>
      )}
    </Card>
  );

  const renderAccountsCard = () => (
    <Card style={styles.accountsCard}>
      <Text style={styles.cardTitle}>账户余额</Text>
      
      {accounts.length === 0 ? (
        <View style={styles.emptyChart}>
          <Ionicons name="wallet-outline" size={48} color="#C7C7CC" />
          <Text style={styles.emptyChartText}>暂无账户</Text>
        </View>
      ) : (
        <View style={styles.accountsList}>
          {accounts.map((account) => (
            <View key={account.id} style={styles.accountItem}>
              <View style={styles.accountLeft}>
                <View style={styles.accountIcon}>
                  <Ionicons
                    name={getAccountIcon(account.type)}
                    size={20}
                    color="#007AFF"
                  />
                </View>
                <Text style={styles.accountName}>{account.name}</Text>
              </View>
              <Text style={styles.accountBalance}>
                ¥{account.balance.toFixed(2)}
              </Text>
            </View>
          ))}
          
          <View style={[styles.accountItem, styles.totalAccount]}>
            <Text style={styles.totalLabel}>总余额</Text>
            <Text style={styles.totalBalance}>
              ¥{accounts.reduce((sum, acc) => sum + acc.balance, 0).toFixed(2)}
            </Text>
          </View>
        </View>
      )}
    </Card>
  );

  const getAccountIcon = (type: string): keyof typeof Ionicons.glyphMap => {
    switch (type) {
      case 'cash':
        return 'cash-outline';
      case 'bank':
        return 'card-outline';
      case 'alipay':
        return 'phone-portrait-outline';
      case 'wechat':
        return 'chatbubble-outline';
      case 'credit_card':
        return 'card-outline';
      default:
        return 'wallet-outline';
    }
  };

  if (isLoading) {
    return <LoadingSpinner text="加载统计数据..." />;
  }

  if (transactions.length === 0) {
    return (
      <View style={styles.container}>
        <EmptyState
          icon="bar-chart-outline"
          title="暂无统计数据"
          description="还没有任何交易记录，快去记账吧！"
          actionTitle="去记账"
          onAction={() => {
            // TODO: 导航到记账页面
          }}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* 时间段选择器 */}
      <View style={styles.periodSelector}>
        {periodOptions.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.periodButton,
              selectedPeriod === option.value && styles.selectedPeriodButton,
            ]}
            onPress={() => setSelectedPeriod(option.value)}
          >
            <Text
              style={[
                styles.periodButtonText,
                selectedPeriod === option.value && styles.selectedPeriodButtonText,
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderOverviewCard()}
        {renderCategoryChart()}
        {renderAccountsCard()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  periodSelector: {
    flexDirection: 'row',
    margin: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
  },
  selectedPeriodButton: {
    backgroundColor: '#007AFF',
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8E8E93',
  },
  selectedPeriodButtonText: {
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  overviewCard: {
    margin: 20,
    marginTop: 0,
    padding: 20,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
  },
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  overviewItem: {
    width: (screenWidth - 80) / 2,
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
  },
  overviewIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  overviewLabel: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 4,
  },
  overviewAmount: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  chartCard: {
    margin: 20,
    marginTop: 0,
    padding: 20,
  },
  emptyChart: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyChartText: {
    marginTop: 12,
    fontSize: 16,
    color: '#8E8E93',
  },
  categoryList: {
    gap: 12,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  categoryCount: {
    fontSize: 14,
    color: '#8E8E93',
  },
  categoryRight: {
    alignItems: 'flex-end',
  },
  categoryAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  categoryPercentage: {
    fontSize: 14,
    color: '#8E8E93',
  },
  accountsCard: {
    margin: 20,
    marginTop: 0,
    marginBottom: 40,
    padding: 20,
  },
  accountsList: {
    gap: 12,
  },
  accountItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  accountLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  accountIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F0F8FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  accountName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
  },
  accountBalance: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  totalAccount: {
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    paddingTop: 16,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  totalBalance: {
    fontSize: 20,
    fontWeight: '700',
    color: '#007AFF',
  },
});

export default StatsScreen;
