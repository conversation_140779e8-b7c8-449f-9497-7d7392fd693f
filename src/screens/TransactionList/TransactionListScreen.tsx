import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Modal,
  Alert,
} from 'react-native';
import {
  useRoute,
  useNavigation,
  useFocusEffect,
} from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { TextInput, Picker, PickerOption } from '../../components/forms';
import {
  Button,
  LoadingSpinner,
  EmptyState,
  Card,
} from '../../components/common';
import {
  BatchOperationBar,
  TagManager,
  AdvancedSearch,
  AdvancedSearchFilters,
} from '../../components/transaction';
import { useAppStore } from '../../stores/appStore';
import { useTransactionStore } from '../../stores/transactionStore';
import { Transaction } from '../../types';

interface TransactionListScreenProps {
  route?: {
    params?: {
      filter?: {
        type?: 'income' | 'expense';
        category_id?: string;
        account_id?: string;
        date_range?: {
          start: string;
          end: string;
        };
      };
    };
  };
}

const TransactionListScreen: React.FC<TransactionListScreenProps> = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { user } = useAppStore();
  const {
    transactions,
    accounts,
    categories,
    isLoading,
    loadTransactions,
    loadAccounts,
    loadCategories,
    deleteTransaction,
  } = useTransactionStore();

  const [searchText, setSearchText] = useState('');
  const [showFilter, setShowFilter] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedAccount, setSelectedAccount] = useState<string>('');

  // 批量操作状态
  const [batchMode, setBatchMode] = useState(false);
  const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(
    new Set()
  );

  // 高级搜索状态
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedSearchFilters>(
    {}
  );

  // 标签管理状态
  const [showTagManager, setShowTagManager] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const initialFilter = (route.params as any)?.filter;

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  useEffect(() => {
    // 应用初始筛选条件
    if (initialFilter) {
      setSelectedType(initialFilter.type || '');
      setSelectedCategory(initialFilter.category_id || '');
      setSelectedAccount(initialFilter.account_id || '');
    }
  }, [initialFilter]);

  useFocusEffect(
    useCallback(() => {
      if (user) {
        loadTransactions(user.id, true);
      }
    }, [user])
  );

  const loadData = async () => {
    if (!user) return;

    try {
      await Promise.all([
        loadTransactions(user.id, true),
        loadAccounts(user.id),
        loadCategories(user.id),
      ]);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const typeOptions: PickerOption[] = [
    { label: '全部类型', value: '' },
    {
      label: '支出',
      value: 'expense',
      icon: 'arrow-up-circle',
      color: '#FF3B30',
    },
    {
      label: '收入',
      value: 'income',
      icon: 'arrow-down-circle',
      color: '#34C759',
    },
  ];

  const categoryOptions: PickerOption[] = [
    { label: '全部分类', value: '' },
    ...categories.map((category) => ({
      label: category.name,
      value: category.id,
      icon: category.icon as keyof typeof Ionicons.glyphMap,
      color: category.color,
    })),
  ];

  const accountOptions: PickerOption[] = [
    { label: '全部账户', value: '' },
    ...accounts.map((account) => ({
      label: account.name,
      value: account.id,
      icon: getAccountIcon(account.type),
    })),
  ];

  function getAccountIcon(type: string): keyof typeof Ionicons.glyphMap {
    switch (type) {
      case 'cash':
        return 'cash-outline';
      case 'bank':
        return 'card-outline';
      case 'alipay':
        return 'phone-portrait-outline';
      case 'wechat':
        return 'chatbubble-outline';
      case 'credit_card':
        return 'card-outline';
      default:
        return 'wallet-outline';
    }
  }

  const filteredTransactions = transactions.filter((transaction) => {
    // 搜索过滤
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      const matchesDescription = transaction.description
        ?.toLowerCase()
        .includes(searchLower);
      const matchesCategory = transaction.category?.name
        .toLowerCase()
        .includes(searchLower);
      const matchesAccount = transaction.account?.name
        .toLowerCase()
        .includes(searchLower);

      if (!matchesDescription && !matchesCategory && !matchesAccount) {
        return false;
      }
    }

    // 类型过滤
    if (selectedType && transaction.type !== selectedType) {
      return false;
    }

    // 分类过滤
    if (selectedCategory && transaction.category_id !== selectedCategory) {
      return false;
    }

    // 账户过滤
    if (selectedAccount && transaction.account_id !== selectedAccount) {
      return false;
    }

    return true;
  });

  const handleDeleteTransaction = (transaction: Transaction) => {
    Alert.alert('确认删除', '确定要删除这条交易记录吗？此操作无法撤销。', [
      { text: '取消', style: 'cancel' },
      {
        text: '删除',
        style: 'destructive',
        onPress: async () => {
          try {
            await deleteTransaction(transaction.id);
            Alert.alert('成功', '交易记录已删除');
          } catch (error) {
            Alert.alert('错误', '删除失败，请重试');
          }
        },
      },
    ]);
  };

  // 批量操作处理函数
  const handleToggleSelection = (transactionId: string) => {
    const newSelected = new Set(selectedTransactions);
    if (newSelected.has(transactionId)) {
      newSelected.delete(transactionId);
    } else {
      newSelected.add(transactionId);
    }
    setSelectedTransactions(newSelected);
  };

  const handleSelectAll = () => {
    const allIds = new Set(filteredTransactions.map((t) => t.id));
    setSelectedTransactions(allIds);
  };

  const handleDeselectAll = () => {
    setSelectedTransactions(new Set());
  };

  const handleBatchDelete = async () => {
    try {
      const deletePromises = Array.from(selectedTransactions).map((id) =>
        deleteTransaction(id)
      );
      await Promise.all(deletePromises);

      Alert.alert('成功', `已删除 ${selectedTransactions.size} 条记录`);
      setSelectedTransactions(new Set());
      setBatchMode(false);
    } catch (error) {
      Alert.alert('错误', '批量删除失败，请重试');
    }
  };

  const handleBatchEdit = () => {
    Alert.alert('功能开发中', '批量编辑功能正在开发中');
  };

  const handleBatchExport = () => {
    Alert.alert('功能开发中', '批量导出功能正在开发中');
  };

  const handleCancelBatch = () => {
    setBatchMode(false);
    setSelectedTransactions(new Set());
  };

  // 高级搜索处理函数
  const handleAdvancedSearch = (filters: AdvancedSearchFilters) => {
    setAdvancedFilters(filters);
  };

  const handleResetSearch = () => {
    setAdvancedFilters({});
    setSearchText('');
    setSelectedType('');
    setSelectedCategory('');
    setSelectedAccount('');
  };

  const renderTransaction = ({ item }: { item: Transaction }) => (
    <Card
      style={styles.transactionCard}
      onPress={() => {
        navigation.navigate(
          'TransactionDetail' as never,
          { transactionId: item.id } as never
        );
      }}
    >
      <View style={styles.transactionHeader}>
        <View style={styles.transactionLeft}>
          <Ionicons
            name={
              item.type === 'income' ? 'arrow-down-circle' : 'arrow-up-circle'
            }
            size={24}
            color={item.type === 'income' ? '#34C759' : '#FF3B30'}
          />
          <View style={styles.transactionInfo}>
            <Text style={styles.transactionDescription}>
              {item.description || item.category?.name}
            </Text>
            <Text style={styles.transactionCategory}>
              {item.category?.name} • {item.account?.name}
            </Text>
          </View>
        </View>

        <View style={styles.transactionRight}>
          <Text
            style={[
              styles.transactionAmount,
              { color: item.type === 'income' ? '#34C759' : '#FF3B30' },
            ]}
          >
            {item.type === 'income' ? '+' : '-'}¥{item.amount.toFixed(2)}
          </Text>
          <Text style={styles.transactionDate}>
            {new Date(item.transaction_date).toLocaleDateString('zh-CN')}
          </Text>
        </View>
      </View>

      <View style={styles.transactionActions}>
        {item.is_ai_generated && (
          <View style={styles.aiTag}>
            <Ionicons name="sparkles" size={12} color="#007AFF" />
            <Text style={styles.aiTagText}>AI识别</Text>
          </View>
        )}

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              navigation.navigate(
                'AddTransaction' as never,
                { editTransaction: item } as never
              );
            }}
          >
            <Ionicons name="create-outline" size={16} color="#007AFF" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteTransaction(item)}
          >
            <Ionicons name="trash-outline" size={16} color="#FF3B30" />
          </TouchableOpacity>
        </View>
      </View>
    </Card>
  );

  const renderFilterModal = () => (
    <Modal
      visible={showFilter}
      transparent
      animationType="slide"
      onRequestClose={() => setShowFilter(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.filterModal}>
          <View style={styles.filterHeader}>
            <Text style={styles.filterTitle}>筛选条件</Text>
            <TouchableOpacity onPress={() => setShowFilter(false)}>
              <Ionicons name="close" size={24} color="#8E8E93" />
            </TouchableOpacity>
          </View>

          <View style={styles.filterContent}>
            <Picker
              label="交易类型"
              value={selectedType}
              options={typeOptions}
              onSelect={(option) => setSelectedType(option.value)}
            />

            <Picker
              label="分类"
              value={selectedCategory}
              options={categoryOptions}
              onSelect={(option) => setSelectedCategory(option.value)}
            />

            <Picker
              label="账户"
              value={selectedAccount}
              options={accountOptions}
              onSelect={(option) => setSelectedAccount(option.value)}
            />
          </View>

          <View style={styles.filterActions}>
            <Button
              title="重置"
              variant="outline"
              onPress={() => {
                setSelectedType('');
                setSelectedCategory('');
                setSelectedAccount('');
              }}
              style={styles.filterButton}
            />
            <Button
              title="应用"
              onPress={() => setShowFilter(false)}
              style={styles.filterButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );

  if (isLoading && transactions.length === 0) {
    return <LoadingSpinner text="加载中..." />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <TextInput
          value={searchText}
          onChangeText={setSearchText}
          placeholder="搜索交易记录..."
          leftIcon="search-outline"
          containerStyle={styles.searchInput}
        />
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilter(true)}
        >
          <Ionicons name="options-outline" size={20} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {filteredTransactions.length === 0 ? (
        <EmptyState
          icon="receipt-outline"
          title="暂无交易记录"
          description="还没有任何交易记录，快去记账吧！"
          actionTitle="去记账"
          onAction={() => navigation.navigate('AddTransaction' as never)}
        />
      ) : (
        <FlatList
          data={filteredTransactions}
          renderItem={renderTransaction}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      )}

      {renderFilterModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  searchInput: {
    flex: 1,
    marginBottom: 0,
    marginRight: 12,
  },
  filterButton: {
    width: 44,
    height: 44,
    borderRadius: 8,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
  },
  transactionCard: {
    marginBottom: 12,
    padding: 16,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionInfo: {
    marginLeft: 12,
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  transactionCategory: {
    fontSize: 14,
    color: '#8E8E93',
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 14,
    color: '#8E8E93',
  },
  transactionActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  aiTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  aiTagText: {
    marginLeft: 4,
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  filterModal: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  filterTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  filterContent: {
    padding: 16,
  },
  filterActions: {
    flexDirection: 'row',
    gap: 12,
    padding: 16,
    paddingBottom: 34,
  },
  filterButton: {
    flex: 1,
  },
});

export default TransactionListScreen;
