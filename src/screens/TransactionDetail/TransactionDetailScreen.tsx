import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Share,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '../../components/common';
import { useTransactionStore } from '../../stores/transactionStore';
import { Transaction } from '../../types';

interface TransactionDetailScreenProps {
  route: {
    params: {
      transactionId: string;
    };
  };
}

const TransactionDetailScreen: React.FC<TransactionDetailScreenProps> = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { transactionId } = (route.params as any);
  
  const {
    transactions,
    accounts,
    categories,
    deleteTransaction,
    loadTransactions,
  } = useTransactionStore();

  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadTransactionDetail();
  }, [transactionId]);

  const loadTransactionDetail = () => {
    const foundTransaction = transactions.find(t => t.id === transactionId);
    
    if (foundTransaction) {
      // 关联账户和分类信息
      const account = accounts.find(a => a.id === foundTransaction.account_id);
      const category = categories.find(c => c.id === foundTransaction.category_id);
      
      setTransaction({
        ...foundTransaction,
        account,
        category,
      });
    }
    
    setIsLoading(false);
  };

  const handleEdit = () => {
    if (transaction) {
      navigation.navigate('AddTransaction' as never, { editTransaction: transaction } as never);
    }
  };

  const handleDelete = () => {
    if (!transaction) return;

    Alert.alert(
      '确认删除',
      '确定要删除这条交易记录吗？此操作无法撤销。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteTransaction(transaction.id);
              Alert.alert('成功', '交易记录已删除', [
                { text: '确定', onPress: () => navigation.goBack() }
              ]);
            } catch (error) {
              Alert.alert('错误', '删除失败，请重试');
            }
          },
        },
      ]
    );
  };

  const handleShare = async () => {
    if (!transaction) return;

    const shareText = `
交易记录
类型：${transaction.type === 'income' ? '收入' : '支出'}
金额：¥${transaction.amount.toFixed(2)}
分类：${transaction.category?.name || '未知'}
账户：${transaction.account?.name || '未知'}
日期：${new Date(transaction.transaction_date).toLocaleDateString('zh-CN')}
${transaction.description ? `描述：${transaction.description}` : ''}
${transaction.notes ? `备注：${transaction.notes}` : ''}
    `.trim();

    try {
      await Share.share({
        message: shareText,
        title: '交易记录分享',
      });
    } catch (error) {
      Alert.alert('错误', '分享失败');
    }
  };

  const getAccountIcon = (type: string): keyof typeof Ionicons.glyphMap => {
    switch (type) {
      case 'cash':
        return 'cash-outline';
      case 'bank':
        return 'card-outline';
      case 'alipay':
        return 'phone-portrait-outline';
      case 'wechat':
        return 'chatbubble-outline';
      case 'credit_card':
        return 'card-outline';
      default:
        return 'wallet-outline';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('zh-CN'),
      time: date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }),
      weekday: date.toLocaleDateString('zh-CN', { weekday: 'long' }),
    };
  };

  if (isLoading) {
    return <LoadingSpinner text="加载中..." />;
  }

  if (!transaction) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color="#FF3B30" />
          <Text style={styles.errorTitle}>交易记录不存在</Text>
          <Text style={styles.errorText}>该交易记录可能已被删除</Text>
          <Button
            title="返回"
            onPress={() => navigation.goBack()}
            style={styles.errorButton}
          />
        </View>
      </View>
    );
  }

  const dateInfo = formatDate(transaction.transaction_date);

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 金额卡片 */}
        <Card style={[
          styles.amountCard,
          { backgroundColor: transaction.type === 'income' ? '#34C759' : '#FF3B30' }
        ]}>
          <View style={styles.amountHeader}>
            <Ionicons
              name={transaction.type === 'income' ? 'arrow-down-circle' : 'arrow-up-circle'}
              size={32}
              color="#FFFFFF"
            />
            <Text style={styles.amountType}>
              {transaction.type === 'income' ? '收入' : '支出'}
            </Text>
          </View>
          <Text style={styles.amountValue}>
            {transaction.type === 'income' ? '+' : '-'}¥{transaction.amount.toFixed(2)}
          </Text>
          {transaction.is_ai_generated && (
            <View style={styles.aiTag}>
              <Ionicons name="sparkles" size={16} color="#FFFFFF" />
              <Text style={styles.aiTagText}>AI识别</Text>
            </View>
          )}
        </Card>

        {/* 基本信息 */}
        <Card style={styles.infoCard}>
          <Text style={styles.sectionTitle}>基本信息</Text>
          
          <View style={styles.infoRow}>
            <View style={styles.infoLabel}>
              <Ionicons
                name={transaction.category?.icon as keyof typeof Ionicons.glyphMap || 'pricetag-outline'}
                size={20}
                color={transaction.category?.color || '#8E8E93'}
              />
              <Text style={styles.infoLabelText}>分类</Text>
            </View>
            <Text style={styles.infoValue}>{transaction.category?.name || '未知分类'}</Text>
          </View>

          <View style={styles.infoRow}>
            <View style={styles.infoLabel}>
              <Ionicons
                name={getAccountIcon(transaction.account?.type || 'other')}
                size={20}
                color="#8E8E93"
              />
              <Text style={styles.infoLabelText}>账户</Text>
            </View>
            <Text style={styles.infoValue}>{transaction.account?.name || '未知账户'}</Text>
          </View>

          <View style={styles.infoRow}>
            <View style={styles.infoLabel}>
              <Ionicons name="calendar-outline" size={20} color="#8E8E93" />
              <Text style={styles.infoLabelText}>日期</Text>
            </View>
            <View style={styles.dateInfo}>
              <Text style={styles.infoValue}>{dateInfo.date}</Text>
              <Text style={styles.dateSubtext}>{dateInfo.weekday} {dateInfo.time}</Text>
            </View>
          </View>

          {transaction.description && (
            <View style={styles.infoRow}>
              <View style={styles.infoLabel}>
                <Ionicons name="text-outline" size={20} color="#8E8E93" />
                <Text style={styles.infoLabelText}>描述</Text>
              </View>
              <Text style={styles.infoValue}>{transaction.description}</Text>
            </View>
          )}

          {transaction.notes && (
            <View style={styles.infoRow}>
              <View style={styles.infoLabel}>
                <Ionicons name="document-text-outline" size={20} color="#8E8E93" />
                <Text style={styles.infoLabelText}>备注</Text>
              </View>
              <Text style={styles.infoValue}>{transaction.notes}</Text>
            </View>
          )}

          {transaction.tags && transaction.tags.length > 0 && (
            <View style={styles.infoRow}>
              <View style={styles.infoLabel}>
                <Ionicons name="pricetags-outline" size={20} color="#8E8E93" />
                <Text style={styles.infoLabelText}>标签</Text>
              </View>
              <View style={styles.tagsContainer}>
                {transaction.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}
        </Card>

        {/* 系统信息 */}
        <Card style={styles.infoCard}>
          <Text style={styles.sectionTitle}>系统信息</Text>
          
          <View style={styles.infoRow}>
            <View style={styles.infoLabel}>
              <Ionicons name="time-outline" size={20} color="#8E8E93" />
              <Text style={styles.infoLabelText}>创建时间</Text>
            </View>
            <Text style={styles.infoValue}>
              {new Date(transaction.created_at).toLocaleString('zh-CN')}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <View style={styles.infoLabel}>
              <Ionicons name="refresh-outline" size={20} color="#8E8E93" />
              <Text style={styles.infoLabelText}>更新时间</Text>
            </View>
            <Text style={styles.infoValue}>
              {new Date(transaction.updated_at).toLocaleString('zh-CN')}
            </Text>
          </View>

          {transaction.is_ai_generated && transaction.ai_confidence && (
            <View style={styles.infoRow}>
              <View style={styles.infoLabel}>
                <Ionicons name="analytics-outline" size={20} color="#8E8E93" />
                <Text style={styles.infoLabelText}>AI置信度</Text>
              </View>
              <Text style={styles.infoValue}>
                {(transaction.ai_confidence * 100).toFixed(1)}%
              </Text>
            </View>
          )}
        </Card>
      </ScrollView>

      {/* 底部操作按钮 */}
      <View style={styles.footer}>
        <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
          <Ionicons name="share-outline" size={20} color="#007AFF" />
          <Text style={styles.actionButtonText}>分享</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleEdit}>
          <Ionicons name="create-outline" size={20} color="#007AFF" />
          <Text style={styles.actionButtonText}>编辑</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.actionButton, styles.deleteButton]} onPress={handleDelete}>
          <Ionicons name="trash-outline" size={20} color="#FF3B30" />
          <Text style={[styles.actionButtonText, styles.deleteButtonText]}>删除</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  scrollView: {
    flex: 1,
  },
  amountCard: {
    margin: 20,
    padding: 24,
    alignItems: 'center',
  },
  amountHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  amountType: {
    marginLeft: 8,
    fontSize: 18,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  amountValue: {
    fontSize: 36,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  aiTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  aiTagText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  infoCard: {
    margin: 20,
    marginTop: 0,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  infoLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  infoLabelText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#8E8E93',
  },
  infoValue: {
    fontSize: 16,
    color: '#1C1C1E',
    fontWeight: '500',
    textAlign: 'right',
    flex: 1,
  },
  dateInfo: {
    alignItems: 'flex-end',
    flex: 1,
  },
  dateSubtext: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 2,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-end',
    flex: 1,
  },
  tag: {
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 4,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 20,
    paddingBottom: 34,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
  actionButton: {
    alignItems: 'center',
    padding: 8,
  },
  actionButtonText: {
    marginTop: 4,
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  deleteButton: {
    // 删除按钮特殊样式
  },
  deleteButtonText: {
    color: '#FF3B30',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 24,
  },
  errorButton: {
    minWidth: 120,
  },
});

export default TransactionDetailScreen;
