import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAppStore } from '../../stores/appStore';
import { useTransactionStore } from '../../stores/transactionStore';
import { useAIStore } from '../../stores/aiStore';
import { AIParseResult } from '../../types';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import AIConfirmCard from '../../components/common/AIConfirmCard';

interface HomeScreenProps {
  navigation: any;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const { user } = useAppStore();
  const {
    transactions,
    accounts,
    categories,
    isLoading,
    loadTransactions,
    loadAccounts,
    loadCategories,
    createTransaction,
  } = useTransactionStore();

  const {
    lastParseResult,
    isScreenshotListening,
    startScreenshotListening,
    stopScreenshotListening,
    clearLastResult,
  } = useAIStore();

  const [refreshing, setRefreshing] = useState(false);
  const [showAIConfirm, setShowAIConfirm] = useState(false);
  const [showQuickAdd, setShowQuickAdd] = useState(false);

  // 计算统计数据
  const todayTransactions = transactions.filter((t) => {
    const today = new Date().toISOString().split('T')[0];
    return t.transaction_date.startsWith(today);
  });

  const todayIncome = todayTransactions
    .filter((t) => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const todayExpense = todayTransactions
    .filter((t) => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalBalance = accounts.reduce(
    (sum, account) => sum + account.balance,
    0
  );

  // 计算月度统计
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  const monthlyTransactions = transactions.filter((t) => {
    const date = new Date(t.transaction_date);
    return (
      date.getMonth() === currentMonth && date.getFullYear() === currentYear
    );
  });

  const monthlyIncome = monthlyTransactions
    .filter((t) => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const monthlyExpense = monthlyTransactions
    .filter((t) => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  // 计算趋势（与上月对比）
  const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
  const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
  const lastMonthTransactions = transactions.filter((t) => {
    const date = new Date(t.transaction_date);
    return (
      date.getMonth() === lastMonth && date.getFullYear() === lastMonthYear
    );
  });

  const lastMonthExpense = lastMonthTransactions
    .filter((t) => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  const expenseTrend =
    lastMonthExpense === 0
      ? 0
      : ((monthlyExpense - lastMonthExpense) / lastMonthExpense) * 100;

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  useEffect(() => {
    // 监听AI解析结果
    if (lastParseResult && !showAIConfirm) {
      setShowAIConfirm(true);
    }
  }, [lastParseResult]);

  const loadData = async () => {
    if (!user) return;

    try {
      await Promise.all([
        loadTransactions(user.id, true),
        loadAccounts(user.id),
        loadCategories(user.id),
      ]);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleToggleScreenshotListening = async () => {
    try {
      if (isScreenshotListening) {
        stopScreenshotListening();
        Alert.alert('已关闭', 'AI截图识别已关闭');
      } else {
        await startScreenshotListening();
        Alert.alert('已开启', 'AI截图识别已开启，截图后会自动分析');
      }
    } catch (error) {
      Alert.alert('错误', error instanceof Error ? error.message : '操作失败');
    }
  };

  const handleAIConfirm = async (aiData: AIParseResult) => {
    try {
      if (!user || accounts.length === 0 || categories.length === 0) {
        Alert.alert('错误', '请先设置账户和分类');
        return;
      }

      // 找到匹配的分类
      const category =
        categories.find(
          (c) => c.name === aiData.category || c.type === aiData.type
        ) || categories.find((c) => c.type === aiData.type);

      if (!category) {
        Alert.alert('错误', '未找到匹配的分类');
        return;
      }

      // 使用第一个账户
      const account = accounts[0];

      const transactionData = {
        user_id: user.id,
        account_id: account.id,
        category_id: category.id,
        amount: aiData.amount,
        type: aiData.type,
        description: aiData.description || aiData.merchant || '',
        transaction_date: new Date().toISOString(),
        is_ai_generated: true,
        ai_confidence: aiData.confidence,
      };

      await createTransaction(transactionData);
      setShowAIConfirm(false);
      clearLastResult();
      Alert.alert('成功', '记账完成！');
    } catch (error) {
      Alert.alert('错误', '记账失败，请重试');
    }
  };

  const handleAICancel = () => {
    setShowAIConfirm(false);
    clearLastResult();
  };

  const handleAIEdit = () => {
    setShowAIConfirm(false);
    navigation.navigate('AddTransaction', { aiData: lastParseResult });
    clearLastResult();
  };

  // 快速记账功能
  const handleQuickExpense = (amount: number, categoryName: string) => {
    if (!user || accounts.length === 0 || categories.length === 0) {
      Alert.alert('错误', '请先设置账户和分类');
      return;
    }

    const category = categories.find(
      (c) => c.name === categoryName && c.type === 'expense'
    );
    if (!category) {
      Alert.alert('错误', `未找到"${categoryName}"分类`);
      return;
    }

    const account = accounts[0]; // 使用第一个账户

    const transactionData = {
      user_id: user.id,
      account_id: account.id,
      category_id: category.id,
      amount,
      type: 'expense' as const,
      description: `快速记账 - ${categoryName}`,
      transaction_date: new Date().toISOString(),
      is_ai_generated: false,
    };

    createTransaction(transactionData)
      .then(() => {
        Alert.alert('成功', `已记录 ¥${amount} 的${categoryName}支出`);
      })
      .catch(() => {
        Alert.alert('错误', '记账失败，请重试');
      });
  };

  if (isLoading && transactions.length === 0) {
    return <LoadingSpinner text="加载中..." />;
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* 头部问候 */}
        <View style={styles.header}>
          <Text style={styles.greeting}>你好，{user?.name || '用户'}</Text>
          <Text style={styles.date}>
            {new Date().toLocaleDateString('zh-CN')}
          </Text>
        </View>

        {/* 总余额卡片 */}
        <View style={styles.balanceCard}>
          <Text style={styles.balanceLabel}>总余额</Text>
          <Text style={styles.balanceAmount}>¥{totalBalance.toFixed(2)}</Text>
        </View>

        {/* 今日统计 */}
        <View style={styles.todayStats}>
          <Text style={styles.sectionTitle}>今日统计</Text>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Ionicons name="arrow-down-circle" size={24} color="#34C759" />
              <Text style={styles.statLabel}>收入</Text>
              <Text style={[styles.statAmount, { color: '#34C759' }]}>
                ¥{todayIncome.toFixed(2)}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="arrow-up-circle" size={24} color="#FF3B30" />
              <Text style={styles.statLabel}>支出</Text>
              <Text style={[styles.statAmount, { color: '#FF3B30' }]}>
                ¥{todayExpense.toFixed(2)}
              </Text>
            </View>
          </View>
        </View>

        {/* 月度统计 */}
        <View style={styles.monthlyStats}>
          <Text style={styles.sectionTitle}>本月统计</Text>
          <View style={styles.monthlyCard}>
            <View style={styles.monthlyRow}>
              <View style={styles.monthlyItem}>
                <Text style={styles.monthlyLabel}>收入</Text>
                <Text style={[styles.monthlyAmount, { color: '#34C759' }]}>
                  ¥{monthlyIncome.toFixed(2)}
                </Text>
              </View>
              <View style={styles.monthlyItem}>
                <Text style={styles.monthlyLabel}>支出</Text>
                <Text style={[styles.monthlyAmount, { color: '#FF3B30' }]}>
                  ¥{monthlyExpense.toFixed(2)}
                </Text>
              </View>
            </View>
            <View style={styles.trendRow}>
              <Text style={styles.trendLabel}>支出趋势</Text>
              <View style={styles.trendValue}>
                <Ionicons
                  name={
                    expenseTrend > 0
                      ? 'trending-up'
                      : expenseTrend < 0
                      ? 'trending-down'
                      : 'remove'
                  }
                  size={16}
                  color={
                    expenseTrend > 0
                      ? '#FF3B30'
                      : expenseTrend < 0
                      ? '#34C759'
                      : '#8E8E93'
                  }
                />
                <Text
                  style={[
                    styles.trendText,
                    {
                      color:
                        expenseTrend > 0
                          ? '#FF3B30'
                          : expenseTrend < 0
                          ? '#34C759'
                          : '#8E8E93',
                    },
                  ]}
                >
                  {expenseTrend === 0
                    ? '持平'
                    : `${Math.abs(expenseTrend).toFixed(1)}%`}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* AI功能区 */}
        <View style={styles.aiSection}>
          <Text style={styles.sectionTitle}>AI智能记账</Text>
          <View style={styles.aiControls}>
            <TouchableOpacity
              style={[
                styles.aiToggle,
                isScreenshotListening && styles.aiToggleActive,
              ]}
              onPress={handleToggleScreenshotListening}
            >
              <Ionicons
                name={isScreenshotListening ? 'eye' : 'eye-off'}
                size={20}
                color={isScreenshotListening ? '#FFFFFF' : '#007AFF'}
              />
              <Text
                style={[
                  styles.aiToggleText,
                  isScreenshotListening && styles.aiToggleTextActive,
                ]}
              >
                {isScreenshotListening ? '截图识别已开启' : '开启截图识别'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 快速操作 */}
        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>快速操作</Text>
          <View style={styles.actionButtons}>
            <Button
              title="手动记账"
              variant="primary"
              onPress={() => navigation.navigate('AddTransaction')}
              style={styles.actionButton}
            />
            <Button
              title="拍照记账"
              variant="outline"
              onPress={() => navigation.navigate('AICapture')}
              style={styles.actionButton}
            />
          </View>

          {/* 快速记账 */}
          <View style={styles.quickExpenseSection}>
            <Text style={styles.quickExpenseTitle}>快速记账</Text>
            <View style={styles.quickExpenseButtons}>
              <TouchableOpacity
                style={styles.quickExpenseButton}
                onPress={() => handleQuickExpense(10, '餐饮')}
              >
                <Ionicons name="restaurant" size={20} color="#FF6B6B" />
                <Text style={styles.quickExpenseText}>餐饮 ¥10</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.quickExpenseButton}
                onPress={() => handleQuickExpense(5, '交通')}
              >
                <Ionicons name="car" size={20} color="#4ECDC4" />
                <Text style={styles.quickExpenseText}>交通 ¥5</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.quickExpenseButton}
                onPress={() => handleQuickExpense(20, '购物')}
              >
                <Ionicons name="bag" size={20} color="#45B7D1" />
                <Text style={styles.quickExpenseText}>购物 ¥20</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.quickExpenseButton}
                onPress={() => handleQuickExpense(15, '娱乐')}
              >
                <Ionicons name="game-controller" size={20} color="#96CEB4" />
                <Text style={styles.quickExpenseText}>娱乐 ¥15</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* 最近交易 */}
        <View style={styles.recentTransactions}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>最近交易</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('TransactionList')}
            >
              <Text style={styles.seeAllText}>查看全部</Text>
            </TouchableOpacity>
          </View>
          {transactions.slice(0, 5).map((transaction) => (
            <View key={transaction.id} style={styles.transactionItem}>
              <View style={styles.transactionLeft}>
                <Ionicons
                  name={
                    transaction.type === 'income'
                      ? 'arrow-down-circle'
                      : 'arrow-up-circle'
                  }
                  size={20}
                  color={transaction.type === 'income' ? '#34C759' : '#FF3B30'}
                />
                <View style={styles.transactionInfo}>
                  <Text style={styles.transactionDescription}>
                    {transaction.description || transaction.category?.name}
                  </Text>
                  <Text style={styles.transactionDate}>
                    {new Date(transaction.transaction_date).toLocaleDateString(
                      'zh-CN'
                    )}
                  </Text>
                </View>
              </View>
              <Text
                style={[
                  styles.transactionAmount,
                  {
                    color:
                      transaction.type === 'income' ? '#34C759' : '#FF3B30',
                  },
                ]}
              >
                {transaction.type === 'income' ? '+' : '-'}¥
                {transaction.amount.toFixed(2)}
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* AI确认对话框 */}
      {lastParseResult && (
        <AIConfirmCard
          visible={showAIConfirm}
          parseResult={lastParseResult}
          onConfirm={handleAIConfirm}
          onCancel={handleAICancel}
          onEdit={handleAIEdit}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
  },
  greeting: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1C1C1E',
  },
  date: {
    fontSize: 16,
    color: '#8E8E93',
    marginTop: 4,
  },
  balanceCard: {
    backgroundColor: '#007AFF',
    margin: 20,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: '700',
    color: '#FFFFFF',
    marginTop: 8,
  },
  todayStats: {
    margin: 20,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 12,
  },
  statsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  statItem: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 8,
  },
  statAmount: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 4,
  },
  aiSection: {
    margin: 20,
    marginTop: 0,
  },
  aiControls: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  aiToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  aiToggleActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  aiToggleText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
    color: '#007AFF',
  },
  aiToggleTextActive: {
    color: '#FFFFFF',
  },
  quickActions: {
    margin: 20,
    marginTop: 0,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  recentTransactions: {
    margin: 20,
    marginTop: 0,
    marginBottom: 40,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  seeAllText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  transactionItem: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionInfo: {
    marginLeft: 12,
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
  },
  transactionDate: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  // 月度统计样式
  monthlyStats: {
    margin: 20,
    marginTop: 0,
  },
  monthlyCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  monthlyRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  monthlyItem: {
    alignItems: 'center',
  },
  monthlyLabel: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 4,
  },
  monthlyAmount: {
    fontSize: 20,
    fontWeight: '600',
  },
  trendRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F2F2F7',
  },
  trendLabel: {
    fontSize: 14,
    color: '#8E8E93',
  },
  trendValue: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  // 快速记账样式
  quickExpenseSection: {
    marginTop: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  quickExpenseTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 12,
  },
  quickExpenseButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickExpenseButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  quickExpenseText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1C1E',
  },
});

export default HomeScreen;
