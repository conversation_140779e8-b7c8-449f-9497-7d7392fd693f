// 应用常量定义

// 默认分类数据
export const DEFAULT_CATEGORIES = {
  expense: [
    { name: '餐饮', icon: 'restaurant', color: '#FF6B6B' },
    { name: '购物', icon: 'bag', color: '#4ECDC4' },
    { name: '交通', icon: 'car', color: '#45B7D1' },
    { name: '娱乐', icon: 'game-controller', color: '#96CEB4' },
    { name: '生活缴费', icon: 'receipt', color: '#FFEAA7' },
    { name: '医疗', icon: 'medical', color: '#DDA0DD' },
    { name: '教育', icon: 'school', color: '#98D8C8' },
    { name: '旅游', icon: 'airplane', color: '#F7DC6F' },
    { name: '转账', icon: 'swap-horizontal', color: '#BB8FCE' },
    { name: '其他', icon: 'ellipsis-horizontal', color: '#BDC3C7' },
  ],
  income: [
    { name: '工资', icon: 'card', color: '#2ECC71' },
    { name: '奖金', icon: 'trophy', color: '#F39C12' },
    { name: '投资收益', icon: 'trending-up', color: '#3498DB' },
    { name: '兼职', icon: 'briefcase', color: '#9B59B6' },
    { name: '礼金', icon: 'gift', color: '#E74C3C' },
    { name: '退款', icon: 'return-up-back', color: '#1ABC9C' },
    { name: '转账收入', icon: 'swap-horizontal', color: '#34495E' },
    { name: '其他收入', icon: 'add-circle', color: '#95A5A6' },
  ],
};

// 默认账户类型
export const DEFAULT_ACCOUNT_TYPES = [
  { type: 'cash', name: '现金', icon: 'cash', color: '#2ECC71' },
  { type: 'bank', name: '银行卡', icon: 'card', color: '#3498DB' },
  { type: 'alipay', name: '支付宝', icon: 'logo-alipay', color: '#1677FF' },
  { type: 'wechat', name: '微信', icon: 'logo-wechat', color: '#07C160' },
  { type: 'credit_card', name: '信用卡', icon: 'card-outline', color: '#E74C3C' },
  { type: 'other', name: '其他', icon: 'wallet', color: '#95A5A6' },
];

// 货币类型
export const CURRENCIES = [
  { code: 'CNY', name: '人民币', symbol: '¥' },
  { code: 'USD', name: '美元', symbol: '$' },
  { code: 'EUR', name: '欧元', symbol: '€' },
  { code: 'JPY', name: '日元', symbol: '¥' },
  { code: 'GBP', name: '英镑', symbol: '£' },
];

// 日期格式
export const DATE_FORMATS = {
  DISPLAY: 'yyyy年MM月dd日',
  API: 'yyyy-MM-dd',
  DATETIME: 'yyyy-MM-dd HH:mm:ss',
};

// AI配置常量
export const AI_CONFIG = {
  // OCR置信度阈值
  OCR_CONFIDENCE_THRESHOLD: 0.5,
  
  // AI解析置信度阈值
  PARSE_CONFIDENCE_THRESHOLD: 0.3,
  
  // 支持的图片格式
  SUPPORTED_IMAGE_FORMATS: ['jpg', 'jpeg', 'png', 'webp'],
  
  // 最大图片大小 (MB)
  MAX_IMAGE_SIZE: 10,
  
  // 截图检测间隔 (毫秒)
  SCREENSHOT_CHECK_INTERVAL: 2000,
  
  // 截图处理超时 (毫秒)
  SCREENSHOT_PROCESS_TIMEOUT: 30000,
};

// 应用主题色彩
export const COLORS = {
  primary: '#007AFF',
  secondary: '#5856D6',
  success: '#34C759',
  warning: '#FF9500',
  error: '#FF3B30',
  
  // 文字颜色
  text: {
    primary: '#1C1C1E',
    secondary: '#8E8E93',
    tertiary: '#C7C7CC',
    inverse: '#FFFFFF',
  },
  
  // 背景颜色
  background: {
    primary: '#FFFFFF',
    secondary: '#F2F2F7',
    tertiary: '#E5E5EA',
  },
  
  // 边框颜色
  border: {
    primary: '#C6C6C8',
    secondary: '#E5E5EA',
  },
  
  // 收支颜色
  transaction: {
    income: '#34C759',
    expense: '#FF3B30',
  },
};

// 字体大小
export const FONT_SIZES = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

// 间距
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

// 圆角
export const BORDER_RADIUS = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 20,
  round: 50,
};

// 阴影
export const SHADOWS = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
};

// 动画持续时间
export const ANIMATION_DURATION = {
  fast: 150,
  normal: 300,
  slow: 500,
};

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  SERVER_ERROR: '服务器错误，请稍后重试',
  VALIDATION_ERROR: '输入信息有误，请检查后重试',
  PERMISSION_DENIED: '权限不足，请检查应用权限设置',
  FILE_TOO_LARGE: '文件过大，请选择较小的文件',
  UNSUPPORTED_FORMAT: '不支持的文件格式',
  OCR_FAILED: 'OCR识别失败，请重试',
  AI_PARSE_FAILED: 'AI解析失败，请手动输入',
  SCREENSHOT_PERMISSION_DENIED: '需要相册权限来检测截图',
};

// 成功消息
export const SUCCESS_MESSAGES = {
  TRANSACTION_CREATED: '记账成功！',
  TRANSACTION_UPDATED: '更新成功！',
  TRANSACTION_DELETED: '删除成功！',
  CATEGORY_CREATED: '分类创建成功！',
  ACCOUNT_CREATED: '账户创建成功！',
  SETTINGS_SAVED: '设置保存成功！',
  DATA_EXPORTED: '数据导出成功！',
  DATA_IMPORTED: '数据导入成功！',
};

// API端点
export const API_ENDPOINTS = {
  // Supabase相关
  SUPABASE_URL: process.env.EXPO_PUBLIC_SUPABASE_URL || '',
  SUPABASE_ANON_KEY: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || '',
  
  // AI服务相关
  OPENAI_API_URL: 'https://api.openai.com/v1/chat/completions',
  GOOGLE_VISION_API_URL: 'https://vision.googleapis.com/v1/images:annotate',
  
  // 自定义API
  CUSTOM_API_BASE_URL: process.env.EXPO_PUBLIC_API_BASE_URL || '',
};
