import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { View } from 'react-native';
import AppNavigator from './src/navigation/AppNavigator';
import { useAppStore } from './src/stores/appStore';
import LoadingSpinner from './src/components/common/LoadingSpinner';

export default function App() {
  const { isLoading, initializeApp } = useAppStore();

  useEffect(() => {
    // 初始化应用
    initializeApp();
  }, []);

  if (isLoading) {
    return (
      <View style={{ flex: 1, backgroundColor: '#F2F2F7' }}>
        <LoadingSpinner text="初始化中..." />
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <AppNavigator />
      <StatusBar style="auto" />
    </View>
  );
}
