{"name": "accounting", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@expo/webpack-config": "^19.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/datetimepicker": "7.2.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "date-fns": "^2.30.0", "expo": "~49.0.15", "expo-camera": "~13.4.4", "expo-constants": "~14.4.2", "expo-font": "~11.4.0", "expo-image-picker": "~14.3.2", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.47.0", "react-native": "0.72.10", "react-native-chart-kit": "^6.12.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-web": "~0.19.6", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.2.14", "typescript": "~5.8.3"}, "private": true}